-- 修复 upsert_fuel_transaction_summary 函数参数类型不匹配问题
-- 问题：函数期望 bigint 参数，但表的 id 字段是 uuid 类型

-- 1. 删除现有的 bigint 版本函数
DROP FUNCTION IF EXISTS order_schema.upsert_fuel_transaction_summary(BIGINT);

-- 2. 创建接受 uuid 参数的新版本函数
CREATE OR REPLACE FUNCTION order_schema.upsert_fuel_transaction_summary(p_fuel_transaction_id UUID)
RETURNS VOID AS $$
DECLARE
    v_summary order_schema.fuel_transactions_summary;
    v_existing_version INTEGER;
BEGIN
    -- 计算新的汇总数据
    -- 注意：这里需要调用 calculate_fuel_transaction_summary 函数
    -- 但该函数可能也需要更新以接受 UUID 参数
    
    -- 临时解决方案：直接在这里计算汇总数据，避免调用可能不存在的函数
    SELECT 
        p_fuel_transaction_id as fuel_transaction_id,
        COALESCE(s.name, '') as station_name,
        COALESCE(s.site_code, '') as site_code,
        '' as employee_name,
        'unknown' as employee_type,
        '' as shift_name,
        NULL as shift_start_time,
        NULL as shift_end_time,
        '' as customer_name,
        '' as customer_phone,
        '' as vehicle_type,
        '' as license_plate,
        '' as promotion_name,
        0.0 as total_discount_amount,
        0.0 as free_liter,
        0.0 as free_liter_amount,
        '' as order_serial_no,
        '' as method_of_payment,
        ft.created_at as payment_time,
        1 as data_version,
        NOW() as last_updated,
        NOW() as created_at,
        'fuel_transactions' as source_tables,
        'trigger_based' as calculation_method
    INTO v_summary
    FROM order_schema.fuel_transactions ft
    LEFT JOIN order_schema.stations s ON ft.station_id = s.id
    WHERE ft.id = p_fuel_transaction_id;
    
    IF NOT FOUND THEN
        RAISE WARNING '燃油交易记录不存在: %', p_fuel_transaction_id;
        RETURN;
    END IF;
    
    -- 检查是否存在现有记录
    SELECT data_version INTO v_existing_version
    FROM order_schema.fuel_transactions_summary
    WHERE fuel_transaction_id = p_fuel_transaction_id;
    
    IF FOUND THEN
        -- 更新现有记录，增加版本号
        v_summary.data_version := v_existing_version + 1;
        v_summary.created_at := (
            SELECT created_at 
            FROM order_schema.fuel_transactions_summary 
            WHERE fuel_transaction_id = p_fuel_transaction_id
        );
        
        UPDATE order_schema.fuel_transactions_summary
        SET
            station_name = v_summary.station_name,
            site_code = v_summary.site_code,
            employee_name = v_summary.employee_name,
            employee_type = v_summary.employee_type,
            shift_name = v_summary.shift_name,
            shift_start_time = v_summary.shift_start_time,
            shift_end_time = v_summary.shift_end_time,
            customer_name = v_summary.customer_name,
            customer_phone = v_summary.customer_phone,
            vehicle_type = v_summary.vehicle_type,
            license_plate = v_summary.license_plate,
            promotion_name = v_summary.promotion_name,
            total_discount_amount = v_summary.total_discount_amount,
            free_liter = v_summary.free_liter,
            free_liter_amount = v_summary.free_liter_amount,
            order_serial_no = v_summary.order_serial_no,
            method_of_payment = v_summary.method_of_payment,
            payment_time = v_summary.payment_time,
            data_version = v_summary.data_version,
            last_updated = v_summary.last_updated,
            source_tables = v_summary.source_tables,
            calculation_method = v_summary.calculation_method
        WHERE fuel_transaction_id = p_fuel_transaction_id;
    ELSE
        -- 插入新记录
        INSERT INTO order_schema.fuel_transactions_summary (
            fuel_transaction_id, station_name, site_code, employee_name, employee_type,
            shift_name, shift_start_time, shift_end_time, customer_name, customer_phone,
            vehicle_type, license_plate, promotion_name, total_discount_amount,
            free_liter, free_liter_amount, order_serial_no, method_of_payment,
            payment_time, data_version, last_updated, created_at,
            source_tables, calculation_method
        ) VALUES (
            v_summary.fuel_transaction_id, v_summary.station_name, v_summary.site_code,
            v_summary.employee_name, v_summary.employee_type, v_summary.shift_name,
            v_summary.shift_start_time, v_summary.shift_end_time, v_summary.customer_name,
            v_summary.customer_phone, v_summary.vehicle_type, v_summary.license_plate,
            v_summary.promotion_name, v_summary.total_discount_amount, v_summary.free_liter,
            v_summary.free_liter_amount, v_summary.order_serial_no, v_summary.method_of_payment,
            v_summary.payment_time, v_summary.data_version, v_summary.last_updated,
            v_summary.created_at, v_summary.source_tables, v_summary.calculation_method
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING '更新燃油交易汇总数据失败 (ID: %): %', p_fuel_transaction_id, SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- 3. 验证函数创建成功
SELECT proname, proargtypes, format_type(proargtypes[0], NULL) as arg_type 
FROM pg_proc 
WHERE proname = 'upsert_fuel_transaction_summary' 
  AND pronamespace = 'order_schema'::regnamespace;
