import { API_BASE_URL } from "@/lib/config";
import {
  Shift,
  ListShiftsResponse,
  StartShiftRequest,
  ShiftStatus,
  ShiftAttendantsResponse,
  ShiftAttendantsQueryParams,
  ShiftQueryParams
} from "@/types/shift";

// 班次报表数据类型定义
export interface ShiftReportData {
  shift_info: {
    id: number;
    shift_number: string;
    station_id: number;
    station_name: string;
    staff_id: number;
    staff_name: string;
    start_time: string;
    end_time: string;
    duration_hours: number;
    status: string;
    created_at: string;
    updated_at: string;
  };
  payment_summary: {
    total_sales: number;
    total_transactions: number;
    payment_methods: Array<{
      method: string;
      method_name: string;
      amount: number;
      transaction_count: number;
      percentage: number;
    }>;
  };
  fuel_summary: {
    total_volume: number;
    total_gross_sales: number;
    total_discount: number;
    total_net_sales: number;
    total_transactions: number;
    fuel_grades: Array<{
      grade: string;
      type: string;
      name: string;
      volume: number;
      gross_amount: number;
      discount_amount: number;
      net_amount: number;
      average_price: number;
      transaction_count: number;
      volume_percentage: number;
    }>;
  };
  merchandise_summary: {
    total_quantity: number;
    total_gross_sales: number;
    total_discount: number;
    total_net_sales: number;
    total_transactions: number;
    top_products: Array<{
      product_id: number;
      product_name: string;
      product_type: string;
      category: string;
      quantity: number;
      unit_price: number;
      gross_amount: number;
      discount_amount: number;
      net_amount: number;
      transaction_count: number;
    }>;
  };
  tera_summary: {
    fuel: {
      gross_sales: number;
      total_discount: number;
      net_sales: number;
      percentage: number;
    };
    merchandise: {
      gross_sales: number;
      total_discount: number;
      net_sales: number;
      percentage: number;
    };
    total: {
      gross_sales: number;
      total_discount: number;
      net_sales: number;
    };
  };
  receipt_info: {
    print_time: string;
    receipt_number: string;
    currency: string;
    timezone: string;
  };
}

// API响应类型
export interface ShiftReportResponse {
  success: boolean;
  message: string;
  data: ShiftReportData;
  meta: {
    generated_at: string;
    processing_time_ms: number;
    data_source: string;
    version: string;
  };
}

/**
 * 获取班次列表
 * @param params 查询参数
 * @returns 班次列表响应
 */
export async function getShifts(params: Record<string, any> = {}): Promise<ListShiftsResponse> {
  const searchParams = new URLSearchParams();
  
  // 添加查询参数
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });
  
  const queryString = searchParams.toString();
  const url = `${API_BASE_URL}/shifts${queryString ? `?${queryString}` : ''}`;
  
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("token")}`,
    },
  });
  
  if (!response.ok) {
    throw new Error(`Failed to fetch shifts: ${response.status}`);
  }
  
  return response.json();
}

/**
 * 获取班次详情
 * @param id 班次ID
 * @returns 班次详情
 */
export async function getShiftById(id: number): Promise<Shift> {
  const response = await fetch(`${API_BASE_URL}/shifts/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("token")}`,
    },
  });
  
  if (!response.ok) {
    throw new Error(`Failed to fetch shift: ${response.status}`);
  }
  
  return response.json();
}

/**
 * 获取当前站点活跃班次
 * @param stationId 站点ID
 * @returns 当前活跃班次
 */
export async function getCurrentShift(stationId: number): Promise<Shift> {
  const response = await fetch(`${API_BASE_URL}/shifts/current/${stationId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("token")}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch current shift: ${response.status}`);
  }

  return response.json();
}

/**
 * 获取站点的班次列表（用于下拉选择）
 * @param stationId 站点ID
 * @param date 日期 (YYYY-MM-DD)
 * @returns 班次列表
 */
export async function getStationShifts(stationId: number, date?: string): Promise<ListShiftsResponse> {
  const params: ShiftQueryParams = {
    station_id: stationId,
    limit: 50, // 获取足够多的班次
    sort_by: 'start_time',
    sort_dir: 'asc'
  };

  if (date) {
    params.date_from = date;
    params.date_to = date;
  }

  return getShifts(params);
}

/**
 * 开始新班次
 * @param data 开始班次的数据
 * @returns 新建的班次
 */
export async function startShift(data: StartShiftRequest): Promise<Shift> {
  const response = await fetch(`${API_BASE_URL}/shifts/start`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("token")}`,
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    throw new Error(`Failed to start shift: ${response.status}`);
  }
  
  return response.json();
}

/**
 * 结束班次
 * @param stationId 站点ID
 * @returns 结束的班次
 */
export async function endShift(stationId: number): Promise<Shift> {
  const response = await fetch(`${API_BASE_URL}/shifts/${stationId}/end`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("token")}`,
    },
  });
  
  if (!response.ok) {
    throw new Error(`Failed to end shift: ${response.status}`);
  }
  
  return response.json();
}

/**
 * 确保站点有活跃班次
 * @param stationId 站点ID
 * @returns 班次信息
 */
export async function ensureShift(stationId: number): Promise<Shift> {
  const response = await fetch(`${API_BASE_URL}/shifts/${stationId}/ensure`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("token")}`,
    },
  });
  
  if (!response.ok) {
    throw new Error(`Failed to ensure shift: ${response.status}`);
  }
  
  return response.json();
}

/**
 * 获取班次报表数据
 * @param id 班次ID
 * @param options 查询选项
 * @returns 班次报表数据
 */
export async function getShiftReport(id: number, options?: {
  format?: string;
  currency?: string;
  timezone?: string;
  include_details?: boolean;
}): Promise<ShiftReportData> {
  const searchParams = new URLSearchParams();
  
  if (options?.format) searchParams.append("format", options.format);
  if (options?.currency) searchParams.append("currency", options.currency);
  if (options?.timezone) searchParams.append("timezone", options.timezone);
  if (options?.include_details !== undefined) {
    searchParams.append("include_details", options.include_details.toString());
  }
  
  const url = `${API_BASE_URL}/shifts/report/${id}${searchParams.toString() ? '?' + searchParams.toString() : ''}`;
  
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("token")}`,
      "Accept-Language": "zh-CN",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
  }

  const result: ShiftReportResponse = await response.json();
  
  if (!result.success) {
    throw new Error(result.message || "Failed to fetch shift report");
  }
  
  return result.data;
}

/**
 * 获取班次员工加油情况
 * @param shiftId 班次ID（支持UUID字符串或数字字符串）
 * @param params 查询参数
 * @returns 班次员工加油情况数据
 */
export async function getShiftAttendants(
  shiftId: string | number,
  params?: ShiftAttendantsQueryParams
): Promise<ShiftAttendantsResponse> {
  const searchParams = new URLSearchParams();
  
  if (params?.attendant_name) {
    searchParams.append("attendant_name", params.attendant_name);
  }
  if (params?.fuel_grade) {
    searchParams.append("fuel_grade", params.fuel_grade);
  }
  if (params?.payment_method) {
    searchParams.append("payment_method", params.payment_method);
  }
  
  const queryString = searchParams.toString();
  const url = `${API_BASE_URL}/shifts/${shiftId}/attendants${queryString ? `?${queryString}` : ''}`;
  
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("token")}`,
    },
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
  }
  
  return response.json();
}
