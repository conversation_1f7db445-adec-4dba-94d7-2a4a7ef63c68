import {
    ApiResponse,
    PaymentMethodsResponse,
    RevenueReportResponse,
    ReceivableReportResponse,
    NozzleSalesResponse,
    TransactionsResponse,
    CategorySalesResponse,
    ReportQueryParams
  } from "@/types/report";
  
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8080/api/v1";  
  // 辅助函数：构建查询参数字符串
  function buildQueryString(params: ReportQueryParams = {}): string {
    const queryParams = new URLSearchParams();
    
    if (params.start_date) queryParams.append("start_date", params.start_date);
    if (params.end_date) queryParams.append("end_date", params.end_date);
    if (params.site_ids) queryParams.append("site_ids", params.site_ids);
    if (params.granularity) queryParams.append("granularity", params.granularity);
    if (params.station_id) queryParams.append("station_id", params.station_id.toString());
    if (params.date_from) queryParams.append("date_from", params.date_from);
    if (params.date_to) queryParams.append("date_to", params.date_to);
    
    const queryString = queryParams.toString();
    return queryString ? `?${queryString}` : "";
  }
  
  // 获取支付方式汇总
  export async function getPaymentMethodsSummary(params: ReportQueryParams = {}): Promise<any> {
    const queryString = buildQueryString(params);
    
    try {
      console.log(`请求URL: ${API_BASE_URL}/reports/payment-methods${queryString}`);
      const response = await fetch(`${API_BASE_URL}/reports/payment-methods${queryString}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`
        }
      });
      
      if (!response.ok) {
        console.error("API请求失败:", response.status, response.statusText);
        throw new Error("获取支付方式汇总失败");
      }
      
      // 直接返回API的响应
      const data = await response.json();
      console.log("API原始响应:", data);
      return data;
    } catch (error) {
      console.error("请求出错:", error);
      throw error;
    }
  }
  
  // 获取聚合收入报表
  export async function getRevenueReport(params: ReportQueryParams): Promise<any> {
    if (!params.start_date || !params.end_date) {
      throw new Error("开始日期和结束日期为必填参数");
    }
    
    const queryString = buildQueryString(params);
    
    try {
      console.log(`请求URL: ${API_BASE_URL}/reports/revenue${queryString}`);
      const response = await fetch(`${API_BASE_URL}/reports/revenue${queryString}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`
        }
      });
      
      if (!response.ok) {
        console.error("API请求失败:", response.status, response.statusText);
        throw new Error("获取收入报表失败");
      }
      
      // 直接返回API的响应
      const data = await response.json();
      console.log("收入报表API原始响应:", data);
      return data;
    } catch (error) {
      console.error("请求出错:", error);
      throw error;
    }
  }
  
  // 获取油品应收汇总
  export async function getReceivableReport(params: ReportQueryParams): Promise<any> {
    if (!params.start_date || !params.end_date) {
      throw new Error("开始日期和结束日期为必填参数");
    }
    
    const queryString = buildQueryString(params);
    
    try {
      console.log(`请求URL: ${API_BASE_URL}/reports/receivable${queryString}`);
      const response = await fetch(`${API_BASE_URL}/reports/receivable${queryString}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`
        }
      });
      
      if (!response.ok) {
        console.error("API请求失败:", response.status, response.statusText);
        throw new Error("获取油品应收汇总失败");
      }
      
      // 直接返回API的响应
      const data = await response.json();
      console.log("油品应收API原始响应:", data);
      return data;
    } catch (error) {
      console.error("请求出错:", error);
      throw error;
    }
  }
  
  // 获取油枪销售汇总
  export async function getNozzleSalesReport(params: ReportQueryParams): Promise<any> {
    if (!params.start_date || !params.end_date) {
      throw new Error("开始日期和结束日期为必填参数");
    }
    
    const queryString = buildQueryString(params);
    
    try {
      console.log(`请求URL: ${API_BASE_URL}/reports/nozzle-sales${queryString}`);
      const response = await fetch(`${API_BASE_URL}/reports/nozzle-sales${queryString}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`
        }
      });
      
      if (!response.ok) {
        console.error("API请求失败:", response.status, response.statusText);
        throw new Error("获取油枪销售汇总失败");
      }
      
      // 直接返回API的响应
      const data = await response.json();
      console.log("油枪销售API原始响应:", data);
      return data;
    } catch (error) {
      console.error("请求出错:", error);
      throw error;
    }
  }
  
  // 获取交易明细数据
  export async function getTransactionsReport(params: ReportQueryParams): Promise<any> {
    if (!params.start_date || !params.end_date) {
      throw new Error("开始日期和结束日期为必填参数");
    }
    
    const queryString = buildQueryString(params);
    
    try {
      console.log(`请求URL: ${API_BASE_URL}/reports/transactions${queryString}`);
      const response = await fetch(`${API_BASE_URL}/reports/transactions${queryString}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`
        }
      });
      
      if (!response.ok) {
        console.error("API请求失败:", response.status, response.statusText);
        throw new Error("获取交易明细数据失败");
      }
      
      // 直接返回API的响应
      const data = await response.json();
      console.log("交易明细API原始响应:", data);
      return data;
    } catch (error) {
      console.error("请求出错:", error);
      throw error;
    }
  }
  
  // 获取按商品分类销售汇总
  export async function getCategorySalesReport(params: ReportQueryParams): Promise<any> {
    if (!params.start_date || !params.end_date) {
      throw new Error("开始日期和结束日期为必填参数");
    }
    
    const queryString = buildQueryString(params);
    
    try {
      console.log(`请求URL: ${API_BASE_URL}/reports/sales-by-category${queryString}`);
      const response = await fetch(`${API_BASE_URL}/reports/sales-by-category${queryString}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`
        }
      });
      
      if (!response.ok) {
        console.error("API请求失败:", response.status, response.statusText);
        throw new Error("获取商品分类销售汇总失败");
      }
      
      // 直接返回API的响应
      const data = await response.json();
      console.log("商品分类销售API原始响应:", data);
      return data;
    } catch (error) {
      console.error("请求出错:", error);
      throw error;
    }
  }
  
  // 获取班次日终报表
  export async function getShiftEODReport(stationId: number, date: string): Promise<any> {
    if (!stationId || !date) {
      throw new Error("站点ID和日期为必填参数");
    }

    const queryParams = new URLSearchParams();
    queryParams.append("station_id", stationId.toString());
    queryParams.append("date", date);

    try {
      console.log(`请求URL: ${API_BASE_URL}/reports/shift-eod?${queryParams.toString()}`);
      const response = await fetch(`${API_BASE_URL}/reports/shift-eod?${queryParams.toString()}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        console.error("API请求失败:", response.status, response.statusText);
        throw new Error("获取班次日终报表失败");
      }

      // 直接返回API的响应
      const data = await response.json();
      console.log("班次日终报表API原始响应:", data);
      return data;
    } catch (error) {
      console.error("请求出错:", error);
      throw error;
    }
  }

  // 获取日终报表 (End of Day Report)
  export async function getEndOfDayReport(params: {
    station_id: number;
    date: string;
    attendant_name?: string;
    fuel_grade?: string;
    payment_method?: string;
    include_summary?: boolean;
  }): Promise<any> {
    if (!params.station_id || !params.date) {
      throw new Error("站点ID和日期为必填参数");
    }

    const queryParams = new URLSearchParams();
    queryParams.append("station_id", params.station_id.toString());
    queryParams.append("date", params.date);
    
    // 添加可选参数
    if (params.attendant_name) queryParams.append("attendant_name", params.attendant_name);
    if (params.fuel_grade) queryParams.append("fuel_grade", params.fuel_grade);
    if (params.payment_method) queryParams.append("payment_method", params.payment_method);
    if (params.include_summary !== undefined) queryParams.append("include_summary", params.include_summary.toString());

    try {
      const fullUrl = `${API_BASE_URL}/reports/end-of-day?${queryParams.toString()}`;
      console.log(`=== API请求详情 ===`);
      console.log(`请求URL: ${fullUrl}`);
      console.log(`API_BASE_URL: ${API_BASE_URL}`);
      console.log(`查询参数: ${queryParams.toString()}`);
      
      const token = getToken();
      console.log(`认证Token: ${token ? '已设置' : '未设置'}`);
      
      const response = await fetch(fullUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`响应状态: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.error("API请求失败:", response.status, response.statusText);
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || "获取日终报表失败");
      }

      // 直接返回API的响应
      const data = await response.json();
      console.log("日终报表API原始响应:", data);
      return data;
    } catch (error) {
      console.error("请求出错:", error);
      throw error;
    }
  }
  
  // 油枪油品泵码报表相关接口
  export interface NozzlePumpReading {
    nozzle_id: string;
    pump_id: string;
    fuel_type: string;
    fuel_grade: string;
    fuel_name: string;
    opening_reading: number;
    closing_reading: number;
    meter_difference: number;
    sales_volume: number;
    sales_amount: number;
    variance: number;
    variance_percentage: number;
    status: 'normal' | 'abnormal';
    last_updated: string;
  }

  export interface NozzlePumpReportData {
    report_header: {
      site_id: number;
      site_name: string;
      report_date: string;
      report_time: string;
      shift_id?: string; // 修改为string类型，支持UUID
      shift_name?: string;
    };
    nozzle_readings: NozzlePumpReading[];
    summary: {
      total_nozzles: number;
      normal_count: number;
      abnormal_count: number;
      total_variance: number;
      total_sales_volume: number;
      total_sales_amount: number;
    };
  }

  export interface NozzlePumpReportResponse {
    code: number;
    message: string;
    data: NozzlePumpReportData;
  }

  export interface NozzlePumpReportParams {
    site_id: number;
    report_date: string;
    shift_id?: string; // 修改为string类型，支持UUID
  }

  /**
   * 获取油枪油品泵码报表
   * @param params 查询参数
   * @returns 油枪油品泵码报表数据
   */
  export const getNozzlePumpReport = async (params: NozzlePumpReportParams): Promise<NozzlePumpReportResponse> => {
    const queryParams = new URLSearchParams();
    queryParams.append('site_id', params.site_id.toString());
    queryParams.append('report_date', params.report_date);
    
    if (params.shift_id) {
      queryParams.append('shift_id', params.shift_id);
    }

    const response = await fetch(`${API_BASE_URL}/reports/nozzle-pump-readings?${queryParams.toString()}`, {
      headers: {
        Authorization: `Bearer ${getToken()}`
      }
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch nozzle pump report');
    }
    
    return response.json();
  };
  
  // 辅助函数：从localStorage获取token
  function getToken(): string {
    if (typeof window !== "undefined") {
      return localStorage.getItem("accessToken") || "";
    }
    return "";
  }