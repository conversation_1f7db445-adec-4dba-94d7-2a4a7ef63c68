// 班次模型 - 按照swagger定义
export interface Shift {
  id: string; // 修改为string类型，对应后端的UUID
  shift_number: string;
  station_id: number;
  start_time: string;
  end_time: string | null;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

// 创建班次请求 - 按照swagger接口
export interface StartShiftRequest {
  station_id: number;
  metadata?: Record<string, any>;
}

// 班次列表响应 - 按照swagger接口
export interface ListShiftsResponse {
  items: Shift[];
  page: number;
  page_size: number;
  total: number;
  total_page: number;
}

// 班次状态枚举
export enum ShiftStatus {
  ACTIVE = 'active',
  CLOSED = 'closed',
}

// 班次查询参数
export interface ShiftQueryParams {
  station_id?: number;
  status?: ShiftStatus;
  shift_number?: string;
  date_from?: string;
  date_to?: string;
  include_deleted?: boolean;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_dir?: 'asc' | 'desc';
}

// 班次员工加油情况相关类型定义
export interface AttendantInfo {
  attendant_name: string;
  staff_card_id: number;
}

export interface FuelGrade {
  fuel_grade: string;
  fuel_name: string;
  fuel_type: string;
  sales_volume: number;
  gross_amount: number;
  discount_amount: number;
  net_amount: number;
  unit_price: number;
  transaction_count: number;
}

export interface FuelSalesTotal {
  total_volume: number;
  total_gross_amount: number;
  total_discount_amount: number;
  total_net_amount: number;
  total_transactions: number;
}

export interface FuelSales {
  by_grade: FuelGrade[];
  total: FuelSalesTotal;
}

export interface PaymentMethod {
  payment_method: string;
  payment_method_name: string;
  total_amount: number;
  transaction_count: number;
  percentage: number;
}

export interface PaymentSummary {
  cash: number;
  non_cash_total: number;
  pvc: number;
  cimb: number;
  bca: number;
  mandiri: number;
  bri: number;
  bni: number;
  voucher: number;
  b2b: number;
  tera: number;
  by_method: PaymentMethod[];
}

export interface AttendantData {
  attendant_info: AttendantInfo;
  transaction_count: number;
  sales_volume_ltr: number;
  sales_amount_idr: number;
  fuel_sales: FuelSales;
  payment_summary: PaymentSummary;
  dry_income: number;
  grand_total: number;
}

export interface ShiftInfo {
  id: number;
  shift_number: string;
  station_id: number;
  station_name: string;
  start_time: string;
  end_time: string | null;
  status: string;
}

export interface ShiftSummary {
  total_attendants: number;
  total_transactions: number;
  total_sales_volume: number;
  total_sales_amount: number;
  total_cash: number;
  total_non_cash: number;
  total_pvc: number;
  total_cimb: number;
  total_bca: number;
  total_mandiri: number;
  total_bri: number;
  total_bni: number;
  total_voucher: number;
  total_b2b: number;
  total_tera: number;
  grand_total: number;
}

export interface ShiftAttendantsData {
  shift_info: ShiftInfo;
  attendants: AttendantData[];
  shift_summary: ShiftSummary;
}

export interface ShiftAttendantsMeta {
  generated_at: string;
  processing_time_ms: number;
  data_source: string;
  version: string;
}

export interface ShiftAttendantsResponse {
  success: boolean;
  message: string;
  data: ShiftAttendantsData;
  meta: ShiftAttendantsMeta;
}

export interface ShiftAttendantsQueryParams {
  attendant_name?: string;
  fuel_grade?: string;
  payment_method?: string;
} 