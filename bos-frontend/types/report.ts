// 支付方式汇总项
export interface PaymentMethodSummary {
    payment_method: string;
    total_amount: number;
    order_count: number;
  }
  
  // 支付方式汇总响应
  export interface PaymentMethodsResponse {
    data: PaymentMethodData[];
    metadata: {
      generatedAt: string;
      totalCount: number;
      filteredCount: number;
      pageCount: number;
      currentPage: number;
      totalRecords: number;
      filteredRecords: number;
      queryTime: number;
      dateRangeConverted: string;
      additionalInfo: any;
    };
  }
  
  // 维度聚合数据项
  export interface DimensionRevenue {
    dimension_key: string;
    total_revenue: number;
  }
  

  
    // 应收汇总数据项
  export interface ReceivableSummary {
    dimension_key: string;
    total_receivable: number;
  }

  // 应收汇总报表响应（用于报表中心）
  export interface ReceivableSummaryResponse {
    items: ReceivableSummary[];
  }

  // 油枪销售汇总项
  export interface NozzleSalesSummary {
    site_id: number;
    nozzle_id: string;
    fuel_type: string;
    fuel_grade: string;
    total_volume: number;
    total_amount: number;
  }
  
  // 油枪销售汇总响应 (旧)
  interface OldNozzleSalesResponse {
    items: NozzleSalesSummary[];
  }
  
  // 油枪销售明细报表类型
  export interface NozzleSalesDataItem {
    siteId: number;
    siteName: string;
    nozzleId: string;
    fuelType: string;
    fuelGrade: string;
    unitPrice: number;
    totalVolume: number;
    totalAmount: number;
    discountAmount: number;
    actualReceivedAmount: number;
    transactionCount: number;
  }
  
  // 油枪销售明细响应（基于Swagger中的handlers.NozzleDetailedSalesResponse）
  export interface NozzleSalesResponse {
    data: NozzleSalesDataItem[];
    metadata: {
      generatedAt: string;
      totalCount: number;
      filteredCount: number;
      pageCount: number;
      currentPage: number;
      totalRecords: number;
      filteredRecords: number;
      queryTime: number;
      dateRangeConverted: string;
      additionalInfo: any;
    };
  }
  
  // 交易明细数据项
  export interface TransactionDetail {
    txn_id: string;
    timestamp: string;
    site_id: number;
    product_category: string;
    amount: number;
    volume: number;
    payment_method: string;
    is_invoiced: boolean;
    nozzle_id: string;
  }
  
  // 交易明细响应
  export interface TransactionsResponse {
    items: TransactionDetail[];
  }
  
  // 商品分类销售汇总项
  export interface CategorySalesSummary {
    dimension_key: string;
    total_amount: number;
  }
  
  // 按商品销售明细报表类型
  export interface CategorySales {
    dimension_key: string;
    total_amount: number;
  }
  
  // 商品分类销售汇总响应
  export interface CategorySalesResponse {
    data: ProductSaleData[];
    metadata: {
      generatedAt: string;
      totalCount: number;
      filteredCount: number;
      pageCount: number;
      currentPage: number;
      totalRecords: number;
      filteredRecords: number;
      queryTime: number;
      dateRangeConverted: string;
      additionalInfo: any;
    };
  }
  
  // 通用查询参数接口
  export interface ReportQueryParams {
    start_date?: string;
    end_date?: string;
    site_ids?: string;
    granularity?: 'day' | 'week' | 'month';
    station_id?: number;
    date_from?: string;
    date_to?: string;
    limit?: number;
    offset?: number;
  }
  
  // API 响应接口
  export interface ApiResponse<T> {
    success: boolean;
    message: string;
    data: T;
  }
  
  // 油品应收汇总报表类型
  export interface ReceivableReportResponse {
    // 燃油销售量
    fuelSalesVolume: number;
    // 燃油销售金额
    fuelSalesAmount: number;
    // 折扣金额
    discountAmount: number;
    // 优惠券折扣
    couponDiscountAmount: number;
    // 积分兑换金额
    pointsRedeemedAmount: number;
    // 总折扣金额
    totalDiscountAmount: number;
    // 使用积分
    pointsUsed: number;
    // 可退款金额
    refundableAmount: number;
    // 可退款折扣
    refundableDiscount: number;
    // 实际退款金额
    actualRefundAmount: number;
    // 实际收款金额
    actualReceivedAmount: number;
  }
  
  // 支付方式销售明细类型
  export interface PaymentMethodDetail {
    paymentMethod: string;
    productName: string;
    liters: number;
    receivableAmount: number;
    discountAmount: number;
    couponDiscountAmount: number;
    pointsRedeemedAmount: number;
    totalDiscountAmount: number;
    pointsUsed: number;
    refundableAmount: number;
    refundableDiscount: number;
    actualRefundAmount: number;
    actualReceivedAmount: number;
  }
  
  export interface PaymentMethodData {
    paymentMethod: string;
    details: PaymentMethodDetail[];
  }
  
  // 收入报表响应（用于班次管理）
  export interface RevenueReportResponse {
    data: {
      fuelReceivableAmount: number;
      nonFuelReceivableAmount: number;
      fuelRefundAmount: number;
      nonFuelRefundAmount: number;
      topUpPrincipalAmount: number;
      topUpBonusAmount: number;
      principalRefundAmount: number;
      bonusRefundAmount: number;
      parentAccountResetAmount: number;
      cardAccountResetAmount: number;
    };
    metadata?: {
      generatedAt: string;
      totalCount?: number;
      filteredCount?: number;
      pageCount?: number;
      currentPage?: number;
      totalRecords?: number;
      filteredRecords?: number;
      queryTime?: number;
      dateRangeConverted?: string;
      additionalInfo?: any;
    };
  }

  // 总收入汇总报表类型（用于报表中心）
  export interface RevenueSummary {
    dimension_key: string;
    total_revenue: number;
  }
  
  export interface RevenueSummaryResponse {
    items: RevenueSummary[];
    metadata?: {
      totalRecords?: number;
      generatedAt?: string;
    };
  }
  
  // 按商品销售明细类型
  export interface ProductSaleDetail {
    paymentMethod: string;
    productName: string;
    liters: number;
    receivableAmount: number;
    discountAmount: number;
    couponDiscountAmount: number;
    pointsRedeemedAmount: number;
    totalDiscountAmount: number;
    pointsUsed: number;
    refundableAmount: number;
    refundableDiscount: number;
    actualRefundAmount: number;
    actualReceivedAmount: number;
  }
  
  export interface ProductSubtotal {
    paymentMethod: string;
    productName: string;
    liters: number;
    receivableAmount: number;
    discountAmount: number;
    couponDiscountAmount: number;
    pointsRedeemedAmount: number;
    totalDiscountAmount: number;
    pointsUsed: number;
    refundableAmount: number;
    refundableDiscount: number;
    actualRefundAmount: number;
    actualReceivedAmount: number;
  }
  
  export interface ProductSaleData {
    productName: string;
    details: ProductSaleDetail[];
    subtotal: ProductSubtotal;
  }

  // 班次日终报表类型定义
  export interface ShiftEODReportHeader {
    company_name: string;
    report_type: string;
    station_id: number;
    station_name: string;
    report_date: string;
    generated_at: string;
  }

  export interface ShiftInfo {
    shift_number: number;
    shift_name: string;
    time_range: string;
    start_time: string;
    end_time: string;
    status: string;
  }

  export interface FuelAttendant {
    employee_id: number;
    employee_name: string;
    employee_code: string;
  }

  export interface FuelGradeDetail {
    fuel_grade: string;
    fuel_name: string;
    fuel_type: string;
    sales_volume: number;
    free_liters: number;
    discount_amount: number;
    net_sales_volume: number;
    unit_price: number;
    gross_amount: number;
    net_amount: number;
    transaction_count: number;
  }

  export interface TotalFuelSales {
    total_volume: number;
    total_gross_amount: number;
    total_discount_amount: number;
    total_net_amount: number;
    total_transactions: number;
  }

  export interface FuelSales {
    by_grade: FuelGradeDetail[];
    total_fuel_sales: TotalFuelSales;
  }

  export interface OtherIncomeItem {
    item_type: string;
    item_name: string;
    quantity: number;
    unit_price: number;
    total_amount: number;
    transaction_count: number;
  }

  export interface OtherIncome {
    items: OtherIncomeItem[];
    total_other_income: number;
    total_other_transactions: number;
  }

  export interface PaymentMethodDetail {
    payment_method: string;
    payment_method_name: string;
    total_amount: number;
    transaction_count: number;
    percentage: number;
  }

  export interface PaymentSummary {
    by_method: PaymentMethodDetail[];
    total_payment: number;
    total_payment_transactions: number;
  }

  export interface ShiftSummary {
    grand_total: number;
    over_cash: number;
    control_point: string;
    comments: string;
  }

  // 员工信息
  export interface EmployeeInfo {
    employee_id: number;
    employee_name: string;
    employee_code: string;
  }

  // 员工汇总信息
  export interface EmployeeSummary {
    total_sales: number;
    total_other_income: number;
    grand_total: number;
    total_transactions: number;
    over_cash: number;
  }

  // 员工数据
  export interface EmployeeData {
    employee_info: EmployeeInfo;
    fuel_sales: FuelSales;
    other_income: OtherIncome;
    payment_summary: PaymentSummary;
    employee_summary: EmployeeSummary;
  }

  export interface ShiftData {
    shift_info: ShiftInfo;
    employees: EmployeeData[];
    shift_summary: ShiftSummary & {
      total_fuel_sales: TotalFuelSales;
      total_other_income: number;
      total_payment: number;
      grand_total: number;
      total_transactions: number;
      over_cash: number;
    };
  }

  export interface DailySummary {
    total_shifts: number;
    total_employees: number;
    total_fuel_volume: number;
    total_gross_sales: number;
    total_discount: number;
    total_net_sales: number;
    total_transactions: number;
  }

  export interface ShiftEODReportData {
    report_header: ShiftEODReportHeader;
    shifts: ShiftData[];
    daily_summary: DailySummary;
  }

  export interface ShiftEODReportResponse {
    code: number;
    message: string;
    data: ShiftEODReportData;
  }

  // 油枪油品泵码报表类型定义
  export interface NozzlePumpReading {
    nozzle_id: string;
    pump_id: string;
    fuel_type: string;
    fuel_grade: string;
    fuel_name: string;
    opening_reading: number;
    closing_reading: number;
    meter_difference: number;
    sales_volume: number;
    sales_amount: number;
    variance: number;
    variance_percentage: number;
    status: 'normal' | 'abnormal';
    last_updated: string;
  }

  export interface NozzlePumpReportData {
    report_header: {
      site_id: number;
      site_name: string;
      report_date: string;
      report_time: string;
      shift_id?: string; // 修改为string类型，支持UUID
      shift_name?: string;
    };
    nozzle_readings: NozzlePumpReading[];
    summary: {
      total_nozzles: number;
      normal_count: number;
      abnormal_count: number;
      total_variance: number;
      total_sales_volume: number;
      total_sales_amount: number;
    };
  }

  export interface NozzlePumpReportResponse {
    code: number;
    message: string;
    data: NozzlePumpReportData;
  }

  export interface NozzlePumpReportParams {
    site_id: number;
    report_date: string;
    shift_id?: string; // 修改为string类型，支持UUID
  }

  // 油枪状态枚举
    export type NozzleStatus = 'normal' | 'abnormal';
  
  // 油枪状态配置
  export interface NozzleStatusConfig {
    normal: {
      label: string;
      color: string;
      bgColor: string;
      threshold: number;
    };
    abnormal: {
      label: string;
      color: string;
      bgColor: string;
      threshold: number;
    };
  }

  // ===== End of Day Report 相关类型定义 =====
  
  // 日终报表请求参数
  export interface EndOfDayReportParams {
    station_id: number;
    date: string;
    attendant_name?: string;
    fuel_grade?: string;
    payment_method?: string;
    include_summary?: boolean;
  }

  // 日终报表头部信息
  export interface EndOfDayReportHeader {
    company_name: string;
    report_type: string;
    station_id: number;
    station_name: string;
    report_date: string;
    generated_at: string;
  }

  // 班次信息
  export interface EndOfDayShiftInfo {
    id: number;
    shift_number: string;
    station_id: number;
    station_name: string;
    start_time: string;
    end_time: string;
    status: string;
    shift_name: string;
    time_range: string;
  }

  // 加油员信息
  export interface EndOfDayAttendantInfo {
    attendant_name: string;
    staff_card_id: number;
    employee_id: number;
    employee_code: string;
  }

  // 油品销售明细
  export interface EndOfDayFuelGradeDetail {
    fuel_grade: string;
    fuel_name: string;
    fuel_type: string;
    sales_volume: number;
    gross_amount: number;
    discount_amount: number;
    net_amount: number;
    unit_price: number;
    transaction_count: number;
  }

  // 油品销售汇总
  export interface EndOfDayFuelSalesTotal {
    total_volume: number;
    total_gross_amount: number;
    total_discount_amount: number;
    total_net_amount: number;
    total_transactions: number;
  }

  // 油品销售数据
  export interface EndOfDayFuelSales {
    by_grade: EndOfDayFuelGradeDetail[];
    total: EndOfDayFuelSalesTotal;
  }

  // 支付方式明细
  export interface EndOfDayPaymentMethodDetail {
    payment_method: string;
    payment_method_name: string;
    total_amount: number;
    transaction_count: number;
    percentage: number;
  }

  // 支付汇总
  export interface EndOfDayPaymentSummary {
    cash: number;
    non_cash_total: number;
    pvc: number;
    cimb: number;
    bca: number;
    mandiri: number;
    bri: number;
    bni: number;
    voucher: number;
    b2b: number;
    tera: number;
    by_method: EndOfDayPaymentMethodDetail[];
  }

  // 其他收入项目
  export interface EndOfDayOtherIncomeItem {
    item_type: string;
    item_name: string;
    quantity: number;
    unit_price: number;
    total_amount: number;
    transaction_count: number;
  }

  // 其他收入
  export interface EndOfDayOtherIncome {
    items: EndOfDayOtherIncomeItem[];
    total_other_income: number;
    total_other_transactions: number;
  }

  // 员工汇总
  export interface EndOfDayEmployeeSummary {
    total_fuel_sales: number;
    total_other_income: number;
    grand_total: number;
    total_transactions: number;
    over_cash: number;
  }

  // 加油员数据
  export interface EndOfDayAttendantData {
    attendant_info: EndOfDayAttendantInfo;
    transaction_count: number;
    sales_volume_ltr: number;
    sales_amount_idr: number;
    fuel_sales: EndOfDayFuelSales;
    payment_summary: EndOfDayPaymentSummary;
    other_income: EndOfDayOtherIncome;
    employee_summary: EndOfDayEmployeeSummary;
  }

  // 班次汇总
  export interface EndOfDayShiftSummary {
    total_attendants: number;
    total_transactions: number;
    total_sales_volume: number;
    total_fuel_sales: number;
    total_other_income: number;
    total_cash: number;
    total_non_cash: number;
    total_pvc: number;
    total_cimb: number;
    total_bca: number;
    total_mandiri: number;
    total_bri: number;
    total_bni: number;
    total_voucher: number;
    total_b2b: number;
    total_tera: number;
    grand_total: number;
    control_point: string;
    comments: string;
    fuel_grades_summary: EndOfDayFuelGradeDetail[];
    payment_methods_summary: EndOfDayPaymentMethodDetail[];
    other_income_summary: EndOfDayOtherIncomeItem[];
  }

  // 班次数据
  export interface EndOfDayShiftData {
    shift_info: EndOfDayShiftInfo;
    attendants: EndOfDayAttendantData[];
    shift_summary: EndOfDayShiftSummary;
  }

  // 日汇总统计
  export interface EndOfDayDailySummary {
    total_shifts: number;
    total_attendants: number;
    total_transactions: number;
    total_fuel_volume: number;
    total_fuel_sales: number;
    total_other_income: number;
    total_gross_sales: number;
    total_discount: number;
    total_net_sales: number;
    payment_methods_summary: EndOfDayPaymentMethodDetail[];
    fuel_grades_summary: EndOfDayFuelGradeDetail[];
    other_income_summary: EndOfDayOtherIncomeItem[];
  }

  // 查询参数
  export interface EndOfDayQueryParams {
    station_id: number;
    date: string;
    filters_applied: string[];
  }

  // 元数据
  export interface EndOfDayReportMeta {
    generated_at: string;
    processing_time_ms: number;
    data_source: string;
    version: string;
    query_params: EndOfDayQueryParams;
  }

  // 日终报表数据
  export interface EndOfDayReportData {
    report_header: EndOfDayReportHeader;
    shifts: EndOfDayShiftData[];
    daily_summary: EndOfDayDailySummary;
  }

  // 日终报表响应
  export interface EndOfDayReportResponse {
    success: boolean;
    message: string;
    data: EndOfDayReportData;
    meta: EndOfDayReportMeta;
  }