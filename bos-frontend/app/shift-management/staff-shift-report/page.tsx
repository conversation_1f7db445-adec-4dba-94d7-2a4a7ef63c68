"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useTranslation } from "@/context/i18n-context";
import { toast } from "@/components/ui/use-toast";
import { getShiftAttendants, getShifts } from "@/api/shift";
import { useSite } from "@/context/site-context";
import { useAuth } from "@/context/auth-context";
import { ShiftAttendantsData, AttendantData, Shift } from '@/types/shift';
import { EmployeeShiftDetailModal } from '@/components/shift-management/EmployeeShiftDetailModal';
import { Printer } from "lucide-react";
import { formatTimezoneTime } from "@/lib/utils";

// 筛选条件组件
interface FilterComponentProps {
  selectedDate: string;
  selectedStation: string;
  selectedShift: string;
  onDateChange: (date: string) => void;
  onStationChange: (station: string) => void;
  onShiftChange: (shift: string) => void;
  onFilter: () => void;
  stations: Array<{ id: number; name: string }>;
  shifts: Shift[];
  isLoadingStations: boolean;
  isLoadingShifts: boolean;
  getStationName: (stationId: string) => string;
}

const FilterComponent: React.FC<FilterComponentProps> = ({
  selectedDate,
  selectedStation,
  selectedShift,
  onDateChange,
  onStationChange,
  onShiftChange,
  onFilter,
  stations,
  shifts,
  isLoadingStations,
  isLoadingShifts,
  getStationName
}) => {
  const { t } = useTranslation();
  


  return (
    <Card className="mb-4 border-gray-300 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium text-gray-900">Filter Conditions</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
          {/* 日期选择 */}
          <div className="space-y-1.5">
            <Label htmlFor="date" className="text-sm font-medium text-gray-700">Date</Label>
            <Input
              id="date"
              type="date"
              value={selectedDate}
              onChange={(e) => onDateChange(e.target.value)}
              className="w-full h-9 border-gray-300 focus:border-gray-500"
            />
          </div>

          {/* 站点显示（只读） */}
          <div className="space-y-1.5">
            <Label htmlFor="station" className="text-sm font-medium text-gray-700">Station</Label>
            <div className="h-9 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700 flex items-center">
              {selectedStation ? getStationName(selectedStation) : "Loading..."}
            </div>
          </div>

          {/* 班次选择 */}
          <div className="space-y-1.5">
            <Label htmlFor="shift" className="text-sm font-medium text-gray-700">Shift</Label>
            <Select value={selectedShift} onValueChange={onShiftChange}>
              <SelectTrigger className="h-9 border-gray-300 focus:border-gray-500">
                <SelectValue placeholder={isLoadingShifts ? "Loading shifts..." : "Select shift"} />
              </SelectTrigger>
              <SelectContent>
                {shifts.map((shift) => (
                  <SelectItem key={shift.id} value={shift.id.toString()}>
                    {shift.shift_number}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 筛选按钮 */}
          <div className="space-y-1.5">
            <Button onClick={onFilter} className="w-full h-9 bg-gray-900 hover:bg-gray-800 text-white" disabled={!selectedStation || !selectedShift}>
              Apply Filter
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// 汇总卡片组件
interface ShiftSummaryCardProps {
  data: ShiftAttendantsData | null;
}

const ShiftSummaryCard: React.FC<ShiftSummaryCardProps> = ({ data }) => {
  if (!data) return null;

  const { shift_summary } = data;
  
  // 格式化数字
  const formatNumber = (num: number) => num.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
  const formatVolume = (num: number) => num.toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 });

  return (
    <div className="w-full mb-4 bg-white border border-gray-300 rounded-lg shadow-sm p-4">
      <div className="text-lg font-medium text-gray-900 mb-3">Shift Summary</div>
      <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
        {/* Total Attendants */}
        <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
          <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">{shift_summary.total_attendants}</div>
          <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
            <span>TOTAL/ATTENDANTS</span>
          </div>
        </div>
        
        {/* Total Transactions */}
        <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
          <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">{shift_summary.total_transactions}</div>
          <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
            <span>TOTAL/TRANSACTIONS</span>
          </div>
        </div>
        
        {/* Total Sales Volume */}
        <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
          <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">{formatVolume(shift_summary.total_sales_volume)}</div>
          <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
            <span>TOTAL/VOLUME (L)</span>
          </div>
        </div>
        
        {/* Total Sales Amount */}
        <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
          <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">{formatNumber(shift_summary.total_sales_amount)}</div>
          <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
            <span>TOTAL/SALES (IDR)</span>
          </div>
        </div>
        
        {/* Grand Total */}
        <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
          <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">{formatNumber(shift_summary.grand_total)}</div>
          <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
            <span>GRAND/TOTAL (IDR)</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// 员工详情表格组件
interface AttendantsTableProps {
  data: ShiftAttendantsData | null;
  onEmployeeClick: (attendant: AttendantData) => void;
}

const AttendantsTable: React.FC<AttendantsTableProps> = ({ data, onEmployeeClick }) => {
  if (!data || !data.attendants || data.attendants.length === 0) {
    return (
      <Card className="border-gray-300 shadow-sm">
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            No attendant data available. Please apply filters to view shift report.
          </div>
        </CardContent>
      </Card>
    );
  }

  const { attendants } = data;
  
  // 格式化数字
  const formatNumber = (num: number) => num.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
  const formatVolume = (num: number) => num.toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 });
  
  // 格式化日期时间 - 使用雅加达时区
  const formatDateTime = (dateTimeString: string | null) => {
    if (!dateTimeString) return 'N/A';
    try {
      // 使用项目的时区工具函数，确保显示雅加达时间
      return formatTimezoneTime(dateTimeString, 'Asia/Jakarta');
    } catch {
      return 'N/A';
    }
  };
  
  // 员工名简写
  const formatEmployeeName = (fullName: string) => {
    const nameParts = fullName.trim().split(' ');
    if (nameParts.length >= 2) {
      return nameParts[0][0] + '. ' + nameParts[nameParts.length - 1];
    }
    return fullName;
  };

  // 指标定义
  const metrics = [
    { key: 'attendant', label: 'Attendant' },
    { key: 'transaction_count', label: 'Transactions' },
    { key: 'sales_volume', label: 'Sales Volume (L)' },
    { key: 'sales_amount', label: 'Sales Amount (IDR)' },
    { key: 'dry_income', label: 'Dry Income (IDR)' },
    { key: 'grand_total', label: 'Grand Total (IDR)' },
    { key: 'cash', label: 'Cash (IDR)' },
    { key: 'non_cash_total', label: 'Non-Cash Total (IDR)' },
    { key: 'pvc', label: 'PVC (IDR)' },
    { key: 'cimb', label: 'CIMB (IDR)' },
    { key: 'bca', label: 'BCA (IDR)' },
    { key: 'mandiri', label: 'MANDIRI (IDR)' },
    { key: 'bri', label: 'BRI (IDR)' },
    { key: 'bni', label: 'BNI (IDR)' },
    { key: 'voucher', label: 'VOUCHER (IDR)' },
    { key: 'b2b', label: 'B2B (IDR)' },
    { key: 'tera', label: 'TERA (IDR)' }
  ];

  // 计算每个员工的各项数据
  const getMetricValue = (attendant: AttendantData, metricKey: string) => {
    switch (metricKey) {
      case 'attendant':
        return attendant.attendant_info.attendant_name;
      case 'transaction_count':
        return attendant.transaction_count;
      case 'sales_volume':
        return formatVolume(attendant.sales_volume_ltr);
      case 'sales_amount':
        return formatNumber(attendant.sales_amount_idr);
      case 'dry_income':
        return formatNumber(attendant.dry_income);
      case 'grand_total':
        return formatNumber(attendant.grand_total);
      case 'cash':
        return formatNumber(attendant.payment_summary.cash);
      case 'non_cash_total':
        return formatNumber(attendant.payment_summary.non_cash_total);
      case 'pvc':
        return formatNumber(attendant.payment_summary.pvc);
      case 'cimb':
        return formatNumber(attendant.payment_summary.cimb);
      case 'bca':
        return formatNumber(attendant.payment_summary.bca);
      case 'mandiri':
        return formatNumber(attendant.payment_summary.mandiri);
      case 'bri':
        return formatNumber(attendant.payment_summary.bri);
      case 'bni':
        return formatNumber(attendant.payment_summary.bni);
      case 'voucher':
        return formatNumber(attendant.payment_summary.voucher);
      case 'b2b':
        return formatNumber(attendant.payment_summary.b2b);
      case 'tera':
        return formatNumber(attendant.payment_summary.tera);
      default:
        return '';
    }
  };

  // 计算Total列
  const getTotalValue = (metricKey: string) => {
    switch (metricKey) {
      case 'attendant':
        return 'Total';
      case 'transaction_count':
        return attendants.reduce((sum, att) => sum + att.transaction_count, 0);
      case 'sales_volume':
        return formatVolume(attendants.reduce((sum, att) => sum + att.sales_volume_ltr, 0));
      case 'sales_amount':
        return formatNumber(attendants.reduce((sum, att) => sum + att.sales_amount_idr, 0));
      case 'dry_income':
        return formatNumber(attendants.reduce((sum, att) => sum + att.dry_income, 0));
      case 'grand_total':
        return formatNumber(attendants.reduce((sum, att) => sum + att.grand_total, 0));
      case 'cash':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.cash, 0));
      case 'non_cash_total':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.non_cash_total, 0));
      case 'pvc':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.pvc, 0));
      case 'cimb':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.cimb, 0));
      case 'bca':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.bca, 0));
      case 'mandiri':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.mandiri, 0));
      case 'bri':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.bri, 0));
      case 'bni':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.bni, 0));
      case 'voucher':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.voucher, 0));
      case 'b2b':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.b2b, 0));
      case 'tera':
        return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.tera, 0));
      default:
        return '';
    }
  };

  return (
    <Card className="border-gray-300 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium text-gray-900">STAFF SHIFT REPORT</CardTitle>
        <div className="text-sm text-gray-600 space-y-1">
          <div>Station: {data.shift_info.station_name}</div>
          <div>Shift: {data.shift_info.shift_number}</div>
          <div>Start Time: {formatDateTime(data.shift_info.start_time)}</div>
          <div>End Time: {formatDateTime(data.shift_info.end_time)}</div>
          <div>Status: {data.shift_info.status}</div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="overflow-x-auto">
          <table className="min-w-max border border-gray-400 bg-white">
            <tbody>
              {metrics.map((metric, rowIdx) => (
                <tr key={metric.key} className="hover:bg-gray-50">
                  <td className={`border border-gray-400 px-2 py-1.5 text-xs font-medium uppercase tracking-wide text-gray-700 whitespace-nowrap text-center ${metric.key === 'attendant' ? 'bg-gray-100' : 'bg-white'}`}>
                    {metric.label}
                  </td>
                  {attendants.map((attendant) => (
                    <td
                      key={attendant.attendant_info.staff_card_id}
                      className={`border border-gray-400 px-2 py-1.5 text-sm text-center ${metric.key === 'attendant' ? 'bg-gray-100' : 'bg-white'} ${metric.key !== 'attendant' ? 'font-mono tabular-nums text-right' : ''}`}
                    >
                      {metric.key === 'attendant' ? (
                        <button
                          onClick={() => onEmployeeClick(attendant)}
                          className="text-gray-900 hover:text-black hover:underline cursor-pointer font-medium"
                          title="Click to view employee shift details"
                        >
                          {formatEmployeeName(attendant.attendant_info.attendant_name)}
                        </button>
                      ) : (
                        <span className="text-gray-900">{getMetricValue(attendant, metric.key)}</span>
                      )}
                    </td>
                  ))}
                  {/* Total列 */}
                  <td className={`border border-gray-400 px-2 py-1.5 text-sm text-center font-medium ${metric.key === 'attendant' ? 'bg-gray-100' : 'bg-white'} ${metric.key !== 'attendant' ? 'font-mono tabular-nums text-right' : ''}`}>
                    <span className="text-gray-900">{getTotalValue(metric.key)}</span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

// 主页面组件
export default function ShiftReportPage() {
  const { t } = useTranslation();
  const { currentSite, availableSites, isLoading: siteLoading } = useSite();
  const { activeStation, activeSite, currentSystemType } = useAuth();
  const [data, setData] = useState<ShiftAttendantsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [isLoadingShifts, setIsLoadingShifts] = useState(false);
  
  // 筛选条件状态 - 使用雅加达时间初始化日期
  const [selectedDate, setSelectedDate] = useState(() => {
    // 获取雅加达时间的当前日期
    const today = new Date();
    // 使用 Intl.DateTimeFormat 获取雅加达时区的日期
    const jakartaDateStr = new Intl.DateTimeFormat('en-CA', {
      timeZone: 'Asia/Jakarta',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(today);
    return jakartaDateStr; // 直接返回 YYYY-MM-DD 格式
  });
  const [selectedStation, setSelectedStation] = useState("");
  const [selectedShift, setSelectedShift] = useState("");

  // 员工详情弹窗状态
  const [employeeModalOpen, setEmployeeModalOpen] = useState(false);
  const [selectedEmployeeData, setSelectedEmployeeData] = useState<any>(null);

  // BOS模式下设置默认站点筛选器值
  useEffect(() => {
    console.log('Staff Shift Report: 检查默认站点设置', {
      currentSystemType,
      activeStation: activeStation?.stationId,
      currentSite: currentSite?.id,
      siteLoading,
      selectedStation
    });

    // 只在BOS模式下设置默认站点
    if (currentSystemType !== 'BOS') {
      console.log('非BOS模式，跳过默认站点设置');
      return;
    }

    // 确保站点数据已加载且还没有选择站点
    if (siteLoading || selectedStation) {
      console.log('站点数据加载中或已有选择，跳过设置');
      return;
    }

    // 优先使用activeStation，如果没有则使用currentSite
    const stationId = activeStation?.stationId || currentSite?.id;

    if (stationId) {
      console.log('设置默认站点:', stationId);
      setSelectedStation(stationId.toString());

      // 记录设置的站点信息 - 使用与end-of-day-report相同的优先级逻辑
      const stationName = (() => {
        if (activeStation) {
          return activeStation.station.site_name;
        } else if (activeSite) {
          return activeSite.siteName;
        } else {
          return availableSites.find(site => site.id === stationId)?.name || 'BP Station';
        }
      })();
      console.log('默认站点设置完成:', { stationId, stationName });

      // 显示成功提示
      toast({
        title: "Station Selected",
        description: `Default station set to: ${stationName}`,
        duration: 2000,
      });
    } else {
      console.log('未找到可用的站点ID');

      // 如果没有找到站点ID，显示警告
      if (!siteLoading) {
        toast({
          title: "No Station Available",
          description: "Please check your station permissions or contact administrator",
          variant: "destructive",
          duration: 3000,
        });
      }
    }
  }, [currentSystemType, activeStation?.stationId, currentSite?.id, siteLoading, selectedStation, availableSites]);

  // 验证站点筛选器与侧边栏显示的一致性
  useEffect(() => {
    if (currentSystemType === 'BOS' && selectedStation && (activeStation || currentSite)) {
      const currentStationId = activeStation?.stationId || currentSite?.id;
      const currentStationName = activeStation?.station?.site_name || currentSite?.name;
      const selectedStationName = availableSites.find(site => site.id.toString() === selectedStation)?.name;

      console.log('站点一致性验证:', {
        侧边栏站点ID: currentStationId,
        侧边栏站点名称: currentStationName,
        筛选器选择站点ID: selectedStation,
        筛选器选择站点名称: selectedStationName,
        是否一致: currentStationId?.toString() === selectedStation
      });

      // 如果不一致，记录警告但不自动修正（避免用户手动选择被覆盖）
      if (currentStationId?.toString() !== selectedStation) {
        console.warn('站点筛选器与侧边栏显示不一致，用户可能手动选择了其他站点');
      }
    }
  }, [currentSystemType, selectedStation, activeStation, currentSite, availableSites]);

  // 获取站点名称 - 使用与end-of-day-report相同的优先级逻辑
  const getStationName = (stationId: string) => {
    // 确保类型匹配进行比较
    const stationIdStr = stationId.toString();
    const activeStationIdStr = activeStation?.stationId?.toString();
    const activeSiteIdStr = activeSite?.siteId?.toString();
    const currentSiteIdStr = currentSite?.id?.toString();

    // 优先从 activeStation 获取当前站点名称
    if (activeStation && activeStationIdStr === stationIdStr) {
      return activeStation.station.site_name;
    }
    // 其次从 activeSite 获取当前站点名称
    if (activeSite && activeSiteIdStr === stationIdStr) {
      return activeSite.siteName;
    }
    // 再次从 currentSite 获取当前站点名称
    if (currentSite && currentSiteIdStr === stationIdStr) {
      return currentSite.name;
    }
    // 备用：从 availableSites 列表中查找
    const station = availableSites.find(s => s.id.toString() === stationIdStr);
    return station?.name || "BP Station";
  };

  // 打印完整报表
  const handlePrintReport = () => {
    if (!data) {
      toast({
        title: "No Data",
        description: "No shift report data available to print",
        variant: "destructive"
      });
      return;
    }

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(generateReportHTML(data));
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
  };

  // 生成完整报表HTML
  const generateReportHTML = (reportData: ShiftAttendantsData): string => {
    const { attendants, shift_summary, shift_info } = reportData;
    
    // 格式化数字
    const formatNumber = (num: number) => num.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
    const formatVolume = (num: number) => num.toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 });
    
    // 员工名简写
    const formatEmployeeName = (fullName: string) => {
      const nameParts = fullName.trim().split(' ');
      if (nameParts.length >= 2) {
        return nameParts[0][0] + '. ' + nameParts[nameParts.length - 1];
      }
      return fullName;
    };

    // 指标定义
    const metrics = [
      { key: 'attendant', label: 'Attendant' },
      { key: 'transaction_count', label: 'Transactions' },
      { key: 'sales_volume', label: 'Sales Volume (L)' },
      { key: 'sales_amount', label: 'Sales Amount (IDR)' },
      { key: 'dry_income', label: 'Dry Income (IDR)' },
      { key: 'grand_total', label: 'Grand Total (IDR)' },
      { key: 'cash', label: 'Cash (IDR)' },
      { key: 'non_cash_total', label: 'Non-Cash Total (IDR)' },
      { key: 'pvc', label: 'PVC (IDR)' },
      { key: 'cimb', label: 'CIMB (IDR)' },
      { key: 'bca', label: 'BCA (IDR)' },
      { key: 'mandiri', label: 'MANDIRI (IDR)' },
      { key: 'bri', label: 'BRI (IDR)' },
      { key: 'bni', label: 'BNI (IDR)' },
      { key: 'voucher', label: 'VOUCHER (IDR)' },
      { key: 'b2b', label: 'B2B (IDR)' },
      { key: 'tera', label: 'TERA (IDR)' }
    ];

    // 计算每个员工的各项数据
    const getMetricValue = (attendant: AttendantData, metricKey: string) => {
      switch (metricKey) {
        case 'attendant':
          return formatEmployeeName(attendant.attendant_info.attendant_name);
        case 'transaction_count':
          return attendant.transaction_count.toString();
        case 'sales_volume':
          return formatVolume(attendant.sales_volume_ltr);
        case 'sales_amount':
          return formatNumber(attendant.sales_amount_idr);
        case 'dry_income':
          return formatNumber(attendant.dry_income);
        case 'grand_total':
          return formatNumber(attendant.grand_total);
        case 'cash':
          return formatNumber(attendant.payment_summary.cash);
        case 'non_cash_total':
          return formatNumber(attendant.payment_summary.non_cash_total);
        case 'pvc':
          return formatNumber(attendant.payment_summary.pvc);
        case 'cimb':
          return formatNumber(attendant.payment_summary.cimb);
        case 'bca':
          return formatNumber(attendant.payment_summary.bca);
        case 'mandiri':
          return formatNumber(attendant.payment_summary.mandiri);
        case 'bri':
          return formatNumber(attendant.payment_summary.bri);
        case 'bni':
          return formatNumber(attendant.payment_summary.bni);
        case 'voucher':
          return formatNumber(attendant.payment_summary.voucher);
        case 'b2b':
          return formatNumber(attendant.payment_summary.b2b);
        case 'tera':
          return formatNumber(attendant.payment_summary.tera);
        default:
          return '';
      }
    };

    // 计算Total列
    const getTotalValue = (metricKey: string) => {
      switch (metricKey) {
        case 'attendant':
          return 'Total';
        case 'transaction_count':
          return attendants.reduce((sum, att) => sum + att.transaction_count, 0).toString();
        case 'sales_volume':
          return formatVolume(attendants.reduce((sum, att) => sum + att.sales_volume_ltr, 0));
        case 'sales_amount':
          return formatNumber(attendants.reduce((sum, att) => sum + att.sales_amount_idr, 0));
        case 'dry_income':
          return formatNumber(attendants.reduce((sum, att) => sum + att.dry_income, 0));
        case 'grand_total':
          return formatNumber(attendants.reduce((sum, att) => sum + att.grand_total, 0));
        case 'cash':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.cash, 0));
        case 'non_cash_total':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.non_cash_total, 0));
        case 'pvc':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.pvc, 0));
        case 'cimb':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.cimb, 0));
        case 'bca':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.bca, 0));
        case 'mandiri':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.mandiri, 0));
        case 'bri':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.bri, 0));
        case 'bni':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.bni, 0));
        case 'voucher':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.voucher, 0));
        case 'b2b':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.b2b, 0));
        case 'tera':
          return formatNumber(attendants.reduce((sum, att) => sum + att.payment_summary.tera, 0));
        default:
          return '';
      }
    };

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shift Report - ${shift_info.station_name}</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .header .info {
            margin-top: 5px;
            font-size: 14px;
        }
        .summary-section {
            margin-bottom: 20px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        .summary-card {
            border: 1px solid #000;
            padding: 10px;
            text-align: center;
        }
        .summary-card .value {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .summary-card .label {
            font-size: 10px;
            text-transform: uppercase;
            font-weight: bold;
        }
        .table-section {
            margin-bottom: 20px;
        }
        .table-section h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        th, td {
            border: 1px solid #000;
            padding: 4px 6px;
            text-align: center;
            font-size: 10px;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-transform: uppercase;
        }
        .attendant-col {
            background-color: #f0f0f0;
        }
        .number-col {
            text-align: right;
            font-family: monospace;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        @media print {
            body {
                padding: 10px;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>SHIFT REPORT</h1>
        <div class="info">
            <div>Station: ${shift_info.station_name}</div>
            <div>Shift: ${shift_info.shift_number} | Status: ${shift_info.status}</div>
            <div>Date: ${selectedDate}</div>
        </div>
    </div>

    <div class="summary-section">
        <h3>Shift Summary</h3>
        <div class="summary-grid">
            <div class="summary-card">
                <div class="value">${shift_summary.total_attendants}</div>
                <div class="label">Total Attendants</div>
            </div>
            <div class="summary-card">
                <div class="value">${shift_summary.total_transactions}</div>
                <div class="label">Total Transactions</div>
            </div>
            <div class="summary-card">
                <div class="value">${formatVolume(shift_summary.total_sales_volume)}</div>
                <div class="label">Total Volume (L)</div>
            </div>
            <div class="summary-card">
                <div class="value">${formatNumber(shift_summary.total_sales_amount)}</div>
                <div class="label">Total Sales (IDR)</div>
            </div>
            <div class="summary-card">
                <div class="value">${formatNumber(shift_summary.grand_total)}</div>
                <div class="label">Grand Total (IDR)</div>
            </div>
        </div>
    </div>

    <div class="table-section">
        <h3>Staff Performance Details</h3>
        <table>
            <tbody>
                ${metrics.map(metric => `
                    <tr>
                        <td class="attendant-col" style="font-weight: bold;">${metric.label}</td>
                        ${attendants.map(attendant => `
                            <td class="${metric.key === 'attendant' ? 'attendant-col' : 'number-col'}">
                                ${getMetricValue(attendant, metric.key)}
                            </td>
                        `).join('')}
                        <td class="${metric.key === 'attendant' ? 'attendant-col' : 'number-col'}" style="font-weight: bold;">
                            ${getTotalValue(metric.key)}
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>

    <div class="footer">
        <p>Generated on ${new Date().toLocaleString()}</p>
        <p>BP-AKR Shift Report System</p>
    </div>
</body>
</html>
    `;
  };

  // 获取班次数据
  const fetchShifts = useCallback(async (stationId: string) => {
    if (!stationId) return;
    
    setIsLoadingShifts(true);
    try {
      const response = await getShifts({
        station_id: parseInt(stationId),
        date_from: selectedDate,
        date_to: selectedDate,
        limit: 50
      });
      
      if (response && response.items) {
        setShifts(response.items);
        // 如果有班次数据且还没有选中班次，自动选中第一个
        if (response.items.length > 0 && !selectedShift) {
          setSelectedShift(response.items[0].id.toString());
        }
      } else {
        setShifts([]);
        setSelectedShift("");
      }
    } catch (error) {
      console.error("Error fetching shifts:", error);
      toast({
        title: "Error",
        description: "Failed to fetch shifts data",
        variant: "destructive"
      });
      setShifts([]);
      setSelectedShift("");
    } finally {
      setIsLoadingShifts(false);
    }
  }, [selectedDate, selectedShift]);

  // 当选择的站点或日期改变时，重新获取班次数据
  useEffect(() => {
    if (selectedStation && selectedDate) {
      fetchShifts(selectedStation);
    }
  }, [selectedStation, selectedDate, fetchShifts]);

  // 处理员工点击事件
  const handleEmployeeClick = (attendant: AttendantData) => {
    // 转换数据格式以适配现有的员工详情模态框
    const employeeShiftData = {
      employee_id: attendant.attendant_info.staff_card_id,
      employee_name: attendant.attendant_info.attendant_name,
      employee_code: `EMP-${attendant.attendant_info.staff_card_id}`,
      shift_number: parseInt(selectedShift),
      shift_name: `Shift ${selectedShift}`,
      time_range: "08:00 - 16:00", // 默认时间范围
      station_name: getStationName(selectedStation),
      report_date: selectedDate,
      fuel_sales: attendant.fuel_sales.by_grade.map(fuel => ({
        fuel_grade: fuel.fuel_grade,
        fuel_name: fuel.fuel_name,
        sales_volume: fuel.sales_volume,
        free_liters: 0,
        discount_amount: fuel.discount_amount,
        net_sales_volume: fuel.sales_volume,
        unit_price: fuel.unit_price,
        sales_amount: fuel.net_amount,
        transactions: [] // 空数组，因为API响应中没有详细交易信息
      })),
      other_income: { 
        nitrogen: 0, 
        other: attendant.dry_income 
      },
      payment_summary: {
        cash: attendant.payment_summary.cash,
        non_cash: attendant.payment_summary.non_cash_total,
        edc_pvs: attendant.payment_summary.pvc,
        edc_cimb: attendant.payment_summary.cimb,
        edc_bca: attendant.payment_summary.bca,
        edc_mandiri: attendant.payment_summary.mandiri,
        voucher: attendant.payment_summary.voucher,
        b2b: attendant.payment_summary.b2b
      },
      total_sales: attendant.grand_total,
      total_volume: attendant.sales_volume_ltr,
      total_transactions: attendant.transaction_count,
      transactions: [] // 空数组，因为API响应中没有详细交易信息
    };
    
    setSelectedEmployeeData(employeeShiftData);
    setEmployeeModalOpen(true);
  };

  // 应用筛选条件
  const handleFilter = useCallback(async () => {
    if (!selectedShift) {
      toast({
        title: "Validation Error",
        description: "Please select a shift to view the report",
        variant: "destructive"
      });
      return;
    }

    // 验证selectedShift是否为有效数字
    const shiftId = parseInt(selectedShift);
    if (isNaN(shiftId) || shiftId <= 0) {
      toast({
        title: "Validation Error",
        description: "Invalid shift ID. Please select a valid shift.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      // 调用真实API
      const response = await getShiftAttendants(shiftId);
      setData(response.data);

      toast({
        title: "Success",
        description: "Shift attendants data loaded successfully",
      });
    } catch (error) {
      console.error("Error fetching shift attendants data:", error);
      toast({
        title: "Error",
        description: "Failed to fetch shift attendants data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedShift]);

  // 当班次选择改变时，自动加载数据
  useEffect(() => {
    if (selectedShift) {
      handleFilter();
    }
  }, [selectedShift, handleFilter]);

  return (
    <div className="max-w-full p-4 space-y-4 bg-gray-50">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900 tracking-tight">Shift Report</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrintReport}
            disabled={!data}
            className="flex items-center gap-2 border-gray-300 hover:bg-gray-50"
          >
            <Printer className="h-4 w-4" />
            Print Report
          </Button>
          <div className="text-xs text-gray-600 bg-white border border-gray-300 px-2 py-1 rounded">
            Using Real API
          </div>
        </div>
      </div>

      {/* 筛选条件组件 */}
      <FilterComponent
        selectedDate={selectedDate}
        selectedStation={selectedStation}
        selectedShift={selectedShift}
        onDateChange={setSelectedDate}
        onStationChange={(value) => {
          setSelectedStation(value);
          setSelectedShift(""); // 重置班次选择
          setData(null); // 清空数据
        }}
        onShiftChange={setSelectedShift}
        onFilter={handleFilter}
        stations={availableSites.map(site => ({
          id: site.id,
          name: (() => {
            // 使用与其他页面一致的站点名称获取逻辑
            // 确保类型匹配进行比较
            const siteIdStr = site.id.toString();
            const activeStationIdStr = activeStation?.stationId?.toString();
            const activeSiteIdStr = activeSite?.siteId?.toString();

            if (activeStation && activeStationIdStr === siteIdStr) {
              return activeStation.station.site_name;
            } else if (activeSite && activeSiteIdStr === siteIdStr) {
              return activeSite.siteName;
            } else {
              return site.name || 'BP Station';
            }
          })()
        }))}
        shifts={shifts}
        isLoadingStations={siteLoading}
        isLoadingShifts={isLoadingShifts}
        getStationName={getStationName}
      />

      {/* 汇总卡片组件 */}
      <ShiftSummaryCard data={data} />

      {/* 员工详情表格 */}
      {isLoading ? (
        <Card className="border-gray-300 shadow-sm">
          <CardContent className="p-6">
            <div className="text-center text-gray-500">Loading shift attendants data...</div>
          </CardContent>
        </Card>
      ) : (
        <AttendantsTable data={data} onEmployeeClick={handleEmployeeClick} />
      )}

      {/* 员工详情弹窗 */}
      <EmployeeShiftDetailModal
        open={employeeModalOpen}
        onOpenChange={setEmployeeModalOpen}
        employeeData={selectedEmployeeData}
      />
    </div>
  );
} 