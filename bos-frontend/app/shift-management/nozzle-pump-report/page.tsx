"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, FileText, Download, XCircle, CheckCircle } from 'lucide-react';
import { useTranslation } from '@/context/i18n-context';
import { useSite } from '@/context/site-context';
import { useAuth } from '@/context/auth-context';
import { getNozzlePumpReport } from "@/api/reports";
import { getStationShifts } from "@/api/shift";
import { NozzlePumpReportData, NozzleStatus } from "@/types/report";
import { Shift } from "@/types/shift";
import { toast } from "@/components/ui/use-toast";
import { PriceDisplay } from '@/components/ui/price-display';
import { Skeleton } from '@/components/ui/skeleton';
import * as XLSX from 'xlsx';

// 状态配置
const statusConfig = {
  normal: {
    label: 'Normal',
    color: 'text-green-700',
    bgColor: 'bg-green-100',
    icon: CheckCircle
  },
  abnormal: {
    label: 'Abnormal',
    color: 'text-red-700',
    bgColor: 'bg-red-100',
    icon: XCircle
  }
};

// 骨架屏组件
const ReportSkeleton = () => (
  <div className="space-y-6">
    {/* 汇总卡片骨架 */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {[1, 2, 3].map(i => (
        <div key={i} className="bg-white p-6 rounded-lg shadow border">
          <Skeleton className="h-4 w-24 mb-2" />
          <Skeleton className="h-8 w-16 mb-1" />
          <Skeleton className="h-3 w-32" />
        </div>
      ))}
    </div>

    {/* 表格骨架 */}
    <div className="bg-white rounded-lg shadow border">
      <div className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="space-y-3">
          {[1, 2, 3, 4, 5].map(i => (
            <div key={i} className="flex space-x-4">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-20" />
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);



// 格式化数字
const formatNumber = (value: number, decimals: number = 3): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value);
};

// 状态组件
const StatusBadge: React.FC<{ status: NozzleStatus }> = ({ status }) => {
  const config = statusConfig[status];

  // 如果配置不存在，使用默认配置
  if (!config) {
    console.warn(`Unknown status: ${status}`);
    const defaultConfig = statusConfig.normal;
    const Icon = defaultConfig.icon;
    return (
      <Badge className={`${defaultConfig.bgColor} ${defaultConfig.color} border-0`}>
        <Icon className="w-3 h-3 mr-1" />
        {status}
      </Badge>
    );
  }

  const Icon = config.icon;

  return (
    <Badge className={`${config.bgColor} ${config.color} border-0`}>
      <Icon className="w-3 h-3 mr-1" />
      {config.label}
    </Badge>
  );
};



export default function NozzlePumpReportPage() {
  const { t } = useTranslation();
  const { currentSite } = useSite();
  const { activeStation, activeSite } = useAuth(); // 添加认证上下文以获取站点信息
  const [reportData, setReportData] = useState<NozzlePumpReportData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(() => {
    // 使用雅加达时间获取当前日期
    const today = new Date();
    // 使用 Intl.DateTimeFormat 获取雅加达时区的日期
    const jakartaDateStr = new Intl.DateTimeFormat('en-CA', {
      timeZone: 'Asia/Jakarta',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(today);
    return jakartaDateStr; // 直接返回 YYYY-MM-DD 格式
  });
  const [selectedShift, setSelectedShift] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [isLoadingShifts, setIsLoadingShifts] = useState(false);

  // 获取班次列表
  const fetchShifts = useCallback(async () => {
    if (!currentSite) return;

    setIsLoadingShifts(true);
    try {
      const response = await getStationShifts(currentSite.id, selectedDate);
      setShifts(response.items);
    } catch (error) {
      console.error("Error fetching shifts:", error);
      // 如果获取班次失败，使用默认班次
      setShifts([]);
    } finally {
      setIsLoadingShifts(false);
    }
  }, [currentSite, selectedDate]);

  // 数据验证函数
  const validateReportData = (data: NozzlePumpReportData): string[] => {
    const errors: string[] = [];

    if (!data.nozzle_readings || data.nozzle_readings.length === 0) {
      errors.push(t('nozzlePumpReport.noDataFound'));
      return errors;
    }

    data.nozzle_readings.forEach((reading, index) => {
      if (!reading.nozzle_id) {
        errors.push(t('nozzlePumpReport.missingNozzleId', { index: index + 1 }));
      }
      if (!reading.pump_id) {
        errors.push(t('nozzlePumpReport.missingPumpId', { index: index + 1 }));
      }
      if (reading.opening_reading < 0 || reading.closing_reading < 0) {
        errors.push(t('nozzlePumpReport.invalidReadings', { nozzleId: reading.nozzle_id }));
      }
      if (reading.sales_volume < 0) {
        errors.push(t('nozzlePumpReport.invalidSalesVolume', { nozzleId: reading.nozzle_id }));
      }
    });

    return errors;
  };

  // 处理报表数据 - 移除前端状态重新计算，信任后端数据
  const processReportData = (data: NozzlePumpReportData): NozzlePumpReportData => {
    // 验证数据
    const validationErrors = validateReportData(data);
    if (validationErrors.length > 0) {
      console.warn('Data validation warnings:', validationErrors);
      // 可以选择显示警告给用户
      validationErrors.forEach(error => {
        toast({
          title: t('nozzlePumpReport.dataValidationWarning'),
          description: error,
          variant: "default",
        });
      });
    }

    // 直接使用后端返回的状态，不再重新计算
    return data;
  };

  // 当站点或日期变化时获取班次列表
  useEffect(() => {
    fetchShifts();
  }, [fetchShifts]);

  // Excel下载功能
  const handleExportExcel = useCallback(() => {
    if (!reportData || !reportData.nozzle_readings || reportData.nozzle_readings.length === 0) {
      toast({
        title: t('nozzlePumpReport.exportError'),
        description: t('nozzlePumpReport.noDataToExport'),
        variant: "destructive",
      });
      return;
    }

    try {
      // 准备Excel数据
      const excelData = reportData.nozzle_readings.map(reading => ({
        [t('nozzlePumpReport.nozzleId')]: reading.nozzle_id,
        [t('nozzlePumpReport.pumpId')]: reading.pump_id,
        [t('nozzlePumpReport.fuelGrade')]: reading.fuel_grade,
        [t('nozzlePumpReport.openingReading')]: reading.opening_reading.toFixed(3),
        [t('nozzlePumpReport.closingReading')]: reading.closing_reading.toFixed(3),
        [t('nozzlePumpReport.meterDifference')]: reading.meter_difference.toFixed(3),
        [t('nozzlePumpReport.salesVolume')]: reading.sales_volume.toFixed(3),
        [t('nozzlePumpReport.salesAmount')]: reading.sales_amount.toLocaleString(),
        [t('nozzlePumpReport.variance')]: reading.variance.toFixed(3),
        [t('nozzlePumpReport.status')]: reading.status === 'normal' ? t('nozzlePumpReport.normal') : t('nozzlePumpReport.abnormal')
      }));

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(excelData);

      // 设置列宽
      const colWidths = [
        { wch: 12 }, // Nozzle ID
        { wch: 12 }, // Pump ID
        { wch: 15 }, // Fuel Grade
        { wch: 15 }, // Opening Reading
        { wch: 15 }, // Closing Reading
        { wch: 15 }, // Meter Difference
        { wch: 15 }, // Sales Volume
        { wch: 15 }, // Sales Amount
        { wch: 12 }, // Variance
        { wch: 10 }  // Status
      ];
      ws['!cols'] = colWidths;

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, t('nozzlePumpReport.reportTitle'));

      // 生成文件名
      const fileName = `nozzle-pump-report-${reportData.report_header?.site_name || 'unknown'}-${reportData.report_header?.report_date || 'unknown'}.xlsx`;

      // 下载文件
      XLSX.writeFile(wb, fileName);

      toast({
        title: t('nozzlePumpReport.exportSuccess'),
        description: t('nozzlePumpReport.fileDownloaded', { fileName }),
        variant: "default",
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: t('nozzlePumpReport.exportError'),
        description: t('nozzlePumpReport.exportFailed'),
        variant: "destructive",
      });
    }
  }, [reportData, t]);

  // 获取报表数据
  const fetchReport = useCallback(async () => {
    if (!currentSite) {
      toast({
        title: "Error",
        description: t('nozzlePumpReport.noSiteSelected'),
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await getNozzlePumpReport({
        site_id: currentSite.id,
        report_date: selectedDate,
        shift_id: selectedShift !== 'all' ? parseInt(selectedShift) : undefined
      });
      
      if (response.code === 200 && response.data) {
        setReportData(processReportData(response.data));
      } else {
        throw new Error(response.message || 'Failed to fetch report');
      }
    } catch (error) {
      console.error("Error fetching nozzle pump report:", error);

      // 显示错误信息而不是使用mock数据
      let errorMessage = t('nozzlePumpReport.errorMessage');
      let errorDetail = 'Unknown error occurred';

      if (error instanceof Error) {
        errorDetail = error.message;
      } else if (typeof error === 'string') {
        errorDetail = error;
      }

      toast({
        title: "Error",
        description: `${errorMessage}: ${errorDetail}`,
        variant: "destructive",
      });

      // 清空报表数据
      setReportData(null);
    } finally {
      setIsLoading(false);
    }
  }, [currentSite, selectedDate, selectedShift, t]);



  // 过滤数据
  const filteredReadings = reportData?.nozzle_readings?.filter(reading => {
    const matchesStatus = statusFilter === 'all' || reading.status === statusFilter;
    const matchesSearch = reading.nozzle_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         reading.pump_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         reading.fuel_grade.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatus && matchesSearch;
  }) || [];

  // 页面加载时自动获取报表数据
  useEffect(() => {
    if (currentSite && selectedDate) {
      fetchReport();
    }
  }, [currentSite, selectedDate, selectedShift, fetchReport]);

  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card className="print:hidden">
          <CardHeader>
            <CardTitle>{t('nozzlePumpReport.title')}</CardTitle>
            <CardDescription>
              {t('nozzlePumpReport.description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 justify-between items-end sm:flex-row">
              <div className="flex gap-4 items-center w-full sm:w-auto">
                <Skeleton className="h-10 w-[200px]" />
                <Skeleton className="h-10 w-[200px]" />
                <Skeleton className="h-10 w-[200px]" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
              </div>
            </div>
          </CardContent>
        </Card>
        <ReportSkeleton />
      </div>
    );
  }

  // 如果没有数据，显示初始状态
  if (!reportData) {
    return (
      <div className="space-y-6">
        <Card className="print:hidden">
          <CardHeader>
            <CardTitle>{t('nozzlePumpReport.title')}</CardTitle>
            <CardDescription>
              {t('nozzlePumpReport.description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 justify-between items-end sm:flex-row">
              <div className="flex gap-4 items-center w-full sm:w-auto">
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t('nozzlePumpReport.reportDate')}</label>
                  <Input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="w-[180px]"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t('nozzlePumpReport.currentSite')}</label>
                  <div className="w-[200px] px-3 py-2 border rounded-md bg-muted text-sm">
                    {(() => {
                      // 使用与end-of-day-report相同的优先级逻辑
                      if (activeStation) {
                        return activeStation.station.site_name;
                      } else if (activeSite) {
                        return activeSite.siteName;
                      } else if (currentSite) {
                        return currentSite.name;
                      } else {
                        return 'No site selected';
                      }
                    })()}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Shift</label>
                  <Select value={selectedShift} onValueChange={setSelectedShift}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Select Shift" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Shifts</SelectItem>
                      <SelectItem value="1">Shift 1</SelectItem>
                      <SelectItem value="2">Shift 2</SelectItem>
                      <SelectItem value="3">Shift 3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" onClick={fetchReport} disabled={isLoading || !currentSite}>
                  <Search className="mr-2 w-4 h-4" />
                  {isLoading ? t('nozzlePumpReport.loading') : t('nozzlePumpReport.generateReport')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="py-12 text-center">
            <FileText className="mx-auto mb-4 w-12 h-12 text-muted-foreground" />
            <h3 className="mb-2 text-lg font-semibold">{t('nozzlePumpReport.noData')}</h3>
            <p className="mb-4 text-center text-muted-foreground">
              {currentSite 
                ? t('nozzlePumpReport.noDataDescription')
                : t('nozzlePumpReport.noSiteSelected')
              }
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-full p-4 space-y-4 bg-gray-50">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900 tracking-tight">Nozzle Pump Report</h1>
      </div>

      {/* 筛选条件区域 */}
      <Card className="mb-4 border-gray-300 shadow-sm print:hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium text-gray-900">Filter Conditions</CardTitle>
          <CardDescription className="text-sm text-gray-600">
            Select date, station and shift to generate Nozzle Pump Report
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
            <div className="space-y-1.5">
              <label className="text-sm font-medium text-gray-700">Date</label>
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full h-9 border-gray-300 focus:border-gray-500"
              />
            </div>

            <div className="space-y-1.5">
              <label className="text-sm font-medium text-gray-700">Station</label>
              <div className="h-9 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700 flex items-center">
                {(() => {
                  // 使用与end-of-day-report相同的优先级逻辑
                  if (activeStation) {
                    return activeStation.station.site_name;
                  } else if (activeSite) {
                    return activeSite.siteName;
                  } else if (currentSite) {
                    return currentSite.name;
                  } else {
                    return t('nozzlePumpReport.noSiteSelected');
                  }
                })()}
              </div>
            </div>

            <div className="space-y-1.5">
              <label className="text-sm font-medium text-gray-700">Shift</label>
              <Select value={selectedShift} onValueChange={setSelectedShift} disabled={isLoadingShifts}>
                <SelectTrigger className="h-9 border-gray-300 focus:border-gray-500">
                  <SelectValue placeholder={isLoadingShifts ? t('nozzlePumpReport.loadingShifts') : t('nozzlePumpReport.selectShift')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('nozzlePumpReport.allShifts')}</SelectItem>
                  {shifts.map((shift) => (
                    <SelectItem key={shift.id} value={shift.id.toString()}>
                      {shift.shift_number}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1.5">
              <div className="flex gap-2">
                <Button onClick={fetchReport} disabled={isLoading || !currentSite} className="h-9 bg-gray-900 hover:bg-gray-800 text-white">
                  <Search className="mr-2 w-4 h-4" />
                  {isLoading ? t('nozzlePumpReport.loading') : 'Query'}
                </Button>
                <Button onClick={handleExportExcel} variant="outline" className="h-9 border-gray-300">
                  <Download className="mr-2 w-4 h-4" />
                  Export
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 统计摘要 */}
      <div className="w-full mb-4 bg-white border border-gray-300 rounded-lg shadow-sm p-4">
        <div className="text-lg font-medium text-gray-900 mb-3">Nozzle Summary</div>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
          {/* Total Nozzles */}
          <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
            <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">{reportData?.summary?.total_nozzles || 0}</div>
            <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
              <span>TOTAL NOZZLES</span>
            </div>
          </div>

          {/* Normal Status */}
          <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
            <div className="text-lg font-semibold text-green-600 mb-1 font-mono tabular-nums">{reportData?.summary?.normal_count || 0}</div>
            <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
              <span>NORMAL</span>
            </div>
          </div>

          {/* Abnormal Status */}
          <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
            <div className="text-lg font-semibold text-red-600 mb-1 font-mono tabular-nums">{reportData?.summary?.abnormal_count || 0}</div>
            <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
              <span>ABNORMAL</span>
            </div>
          </div>

          {/* Total Sales Volume */}
          <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
            <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">{formatNumber(reportData?.summary?.total_sales_volume || 0)} L</div>
            <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
              <span>TOTAL VOLUME</span>
            </div>
          </div>

          {/* Total Sales Amount */}
          <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
            <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">
              <PriceDisplay amount={reportData?.summary?.total_sales_amount || 0} />
            </div>
            <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
              <span>TOTAL SALES</span>
            </div>
          </div>
        </div>
      </div>

      {/* 详细数据表格 */}
      <Card className="border-gray-300 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium text-gray-900">NOZZLE PUMP READINGS</CardTitle>
          <CardDescription className="text-sm text-gray-600">
            Showing {filteredReadings.length} of {reportData?.nozzle_readings?.length || 0} nozzles
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          {/* 搜索和过滤 */}
          <div className="flex flex-col gap-4 mb-6 sm:flex-row print:hidden">
            <div className="flex-1">
              <Input
                placeholder={t('nozzlePumpReport.searchNozzle')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm border-gray-300"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px] border-gray-300">
                <SelectValue placeholder={t('nozzlePumpReport.filterByStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('nozzlePumpReport.allStatuses')}</SelectItem>
                <SelectItem value="normal">{t('nozzlePumpReport.normalStatus')}</SelectItem>
                <SelectItem value="abnormal">{t('nozzlePumpReport.abnormalStatus')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 数据表格 */}
          <div className="overflow-x-auto">
            <Table className="border border-gray-400 bg-white">
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="border border-gray-300 font-bold text-gray-900">{t('nozzlePumpReport.nozzleId')}</TableHead>
                  <TableHead className="border border-gray-300 font-bold text-gray-900">{t('nozzlePumpReport.pumpId')}</TableHead>
                  <TableHead className="border border-gray-300 font-bold text-gray-900">{t('nozzlePumpReport.fuelGrade')}</TableHead>
                  <TableHead className="border border-gray-300 font-bold text-gray-900 text-right">{t('nozzlePumpReport.openingReading')}</TableHead>
                  <TableHead className="border border-gray-300 font-bold text-gray-900 text-right">{t('nozzlePumpReport.closingReading')}</TableHead>
                  <TableHead className="border border-gray-300 font-bold text-gray-900 text-right">{t('nozzlePumpReport.meterDifference')}</TableHead>
                  <TableHead className="border border-gray-300 font-bold text-gray-900 text-right">{t('nozzlePumpReport.salesVolume')}</TableHead>
                  <TableHead className="border border-gray-300 font-bold text-gray-900 text-right">{t('nozzlePumpReport.variance')}</TableHead>
                  <TableHead className="border border-gray-300 font-bold text-gray-900 text-center">{t('nozzlePumpReport.status')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredReadings.map((reading) => (
                  <TableRow key={reading.nozzle_id} className={`hover:bg-gray-50 ${reading.status === 'abnormal' ? 'bg-red-50' : ''}`}>
                    <TableCell className="border border-gray-300 font-medium">{reading.nozzle_id}</TableCell>
                    <TableCell className="border border-gray-300">{reading.pump_id}</TableCell>
                    <TableCell className="border border-gray-300">
                      <div className="font-medium">{reading.fuel_grade}</div>
                    </TableCell>
                    <TableCell className="border border-gray-300 text-right font-mono">{formatNumber(reading.opening_reading)} L</TableCell>
                    <TableCell className="border border-gray-300 text-right font-mono">{formatNumber(reading.closing_reading)} L</TableCell>
                    <TableCell className="border border-gray-300 text-right font-mono">{formatNumber(reading.meter_difference)} L</TableCell>
                    <TableCell className="border border-gray-300 text-right font-mono">{formatNumber(reading.sales_volume)} L</TableCell>
                    <TableCell className="border border-gray-300 text-right font-mono">
                      <span className={reading.variance > 0 ? 'text-red-600 font-bold' : reading.variance < 0 ? 'text-blue-600 font-bold' : 'text-gray-600'}>
                        {reading.variance > 0 ? '+' : ''}{formatNumber(reading.variance)} L
                      </span>
                    </TableCell>
                    <TableCell className="border border-gray-300 text-center">
                      <StatusBadge status={reading.status} />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredReadings.length === 0 && (
            <div className="py-8 text-center text-muted-foreground">
              {t('nozzlePumpReport.noNozzlesFound')}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 状态说明 */}
      <Card className="print:hidden">
        <CardHeader>
          <CardTitle>{t('nozzlePumpReport.statusLegend')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <StatusBadge status="normal" />
              <div className="flex-1">
                <div className="font-medium text-sm">{t('nozzlePumpReport.normal')}</div>
                <div className="text-sm text-muted-foreground">{t('nozzlePumpReport.normalStatusDescription')}</div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <StatusBadge status="abnormal" />
              <div className="flex-1">
                <div className="font-medium text-sm">{t('nozzlePumpReport.abnormal')}</div>
                <div className="text-sm text-muted-foreground">{t('nozzlePumpReport.abnormalStatusDescription')}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}