"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileText,
  Download,
  RefreshCw,
  FileSpreadsheet
} from "lucide-react";
import * as XLSX from 'xlsx';
import { useTranslation } from "@/context/i18n-context";
import { toast } from "@/components/ui/use-toast";
import { formatTimezoneTime } from "@/lib/utils";
import { useAuth } from "@/context/auth-context"; // 引入新的上下文
import { getEndOfDayReport } from '@/api/reports';
import { getFuelColor, getFuelDisplayName, BP_BRAND_COLORS } from '@/utils/fuel-colors';

// 数据接口定义
interface TransactionDetail {
  // 基础信息
  stationName: string;
  stationId: string;
  transactionDateTime: string;
  
  // 设备信息
  dispenserNo: number;
  pumpNo: number;
  nozzleNo: number;
  globalNozzleProduct: string;
  
  // 人员信息
  attendantName: string;
  shiftName: string;
  
  // 交易数据
  unitPrice: number;
  volumeLtr: number;
  amountRp: number;
  discountFuel: number;
  netAmountRp: number;
  
  // 订单信息
  orderSerialNo: string;
  methodOfPayment: string;
  paymentTime: string;
  fccSerialNo: string;
  
  // 时间信息
  nozzleStartFillingTime: string;
  nozzleHangupTime: string;
  
  // 计数器信息
  startTotalizerCount: number;
  endTotalizerCount: number;
  discrepancy: number;
  status: 'Normal' | 'Alert 1' | 'Alert 2';
}

interface FuelGradeData {
  fuel_grade: string;
  fuel_name: string;
  fuel_type?: string;
  sales_volume: number;
  free_liters: number;
  discount_amount: number;
  net_sales_volume: number;
  unit_price: number;
  sales_amount: number;
  gross_amount?: number;  // 毛销售额
  net_amount?: number;    // 净销售额
  transaction_count?: number; // 交易笔数
  // 添加交易详情数组
  transactions: TransactionDetail[];
}

interface PaymentMethodDetail {
  payment_method: string;
  payment_method_name: string;
  total_amount: number;
  transaction_count: number;
  percentage: number;
}

interface OtherIncomeItem {
  item_type: string;
  item_name: string;
  quantity?: number;
  unit_price?: number;
  total_amount: number;
  transaction_count?: number;
}

interface AttendantData {
  employee_id: number;
  employee_name: string;
  employee_code: string;
  fuel_sales: {
    by_grade: FuelGradeData[];
    total: {
      total_volume: number;
      total_gross_amount: number;
      total_discount_amount: number;
      total_net_amount: number;
      total_transactions: number;
    };
  };
  other_income: {
    nitrogen: number;
    other: number;
    items?: OtherIncomeItem[];  // 完整的其他收入明细
    total_other_income?: number;
    total_other_transactions?: number;
  };
  payment_summary: {
    cash: number;
    non_cash_total: number;
    pvc?: number;
    cimb?: number;
    bca?: number;
    mandiri?: number;
    bri?: number;
    bni?: number;
    voucher?: number;
    b2b?: number;
    tera?: number;
    by_method?: PaymentMethodDetail[];
  };
  // 添加该加油员的所有交易详情
  allTransactions: TransactionDetail[];
}

interface ShiftSummary {
  total_attendants: number;
  total_transactions: number;
  total_sales_volume: number;
  total_fuel_sales: number;
  total_other_income: number;
  total_cash: number;
  total_non_cash: number;
  total_pvc?: number;
  total_cimb?: number;
  total_bca?: number;
  total_mandiri?: number;
  total_bri?: number;
  total_bni?: number;
  total_voucher?: number;
  total_b2b?: number;
  total_tera?: number;
  grand_total: number;
  control_point: string;
  comments: string;
  fuel_grades_summary?: Array<{
    fuel_grade: string;
    fuel_name: string;
    fuel_type: string;
    total_volume: number;
    total_gross_amount: number;
    total_discount_amount: number;
    total_net_amount: number;
    transaction_count: number;
    average_price: number;
  }>;
  payment_methods_summary?: Array<{
    payment_method: string;
    payment_method_name: string;
    total_amount: number;
    transaction_count: number;
  }>;
  other_income_summary?: Array<{
    item_type: string;
    item_name: string;
    total_amount: number;
    transaction_count: number;
  }>;
}

interface ShiftData {
  shift_number: number;
  shift_name: string;
  time_range: string;
  attendants: AttendantData[];
  control_point: string;
  comments: string;
  allTransactions: TransactionDetail[];
  shift_manager: {
    id: number;
    name: string;
    code: string;
  };
  shift_summary?: ShiftSummary;
}



interface EODReportData {
  report_header: {
    company_name: string;
    report_type: string;
    station_id: number;
    station_name: string;
    report_date: string;
  };
  shifts: ShiftData[];
  daily_summary: {
    fuel_grades_summary: Array<{
      fuel_grade: string;
      fuel_name: string;
      fuel_type: string;
      total_volume: number;
      total_gross_amount: number;
      total_discount_amount: number;
      total_net_amount: number;
      transaction_count: number;
      average_price: number;
    }>;
    payment_methods_summary: Array<{
      payment_method: string;
      payment_method_name: string;
      total_amount: number;
      transaction_count: number;
    }>;
    other_income_summary: Array<{
      item_type: string;
      item_name: string;
      total_amount: number;
      transaction_count: number;
    }>;
  };
  // 添加所有交易详情
  allTransactions: TransactionDetail[];
}

// 删除MOCK_DATA和generateTransactionDetail函数，现在完全依赖API数据

// 格式化函数
const formatIDR = (amount: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount).replace('Rp', '').trim();
};

const formatVolume = (volume: number) => {
  return volume.toFixed(3);
};

// 格式化日期时间 - 使用雅加达时区
const formatDateTime = (dateTimeString: string | null) => {
  if (!dateTimeString) return 'N/A';
  try {
    // 使用项目的时区工具函数，确保显示雅加达时间
    return formatTimezoneTime(dateTimeString, 'Asia/Jakarta');
  } catch {
    return 'N/A';
  }
};

// 辅助函数：根据油品类型查找燃油数据
const findFuelData = (fuelSales: any, fuelType: string) => {
  // 处理新的数据结构：fuelSales.by_grade 或者旧的数组结构
  const fuelArray = fuelSales?.by_grade || fuelSales || [];
  return fuelArray.find((f: any) => {
    const fuelGrade = f.fuel_grade?.toLowerCase() || '';
    switch (fuelType) {
      case '92':
        return fuelGrade === 'bp 92' || fuelGrade.includes('92');
      case '95':
        return fuelGrade === 'bp 95' || fuelGrade === 'bp ultimate';
      case 'diesel':
        return fuelGrade === 'bp ultimate diesel' || fuelGrade.includes('diesel');
      default:
        return false;
    }
  });
};

// 格式化日期为mock数据需要的格式
const formatDateToMock = (dateStr: string): string => {
  const date = new Date(dateStr);
  const day = String(date.getDate()).padStart(2, '0');
  const month = date.toLocaleString('en-GB', { month: 'short' });
  const year = String(date.getFullYear()).slice(-2);
  return `${day}-${month}-${year}`;
};

// Excel下载函数
const downloadAsExcel = (data: any[], filename: string) => {
  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "EOD Report");
  
  // 设置列宽
  const colWidths = Object.keys(data[0] || {}).map(key => ({ wch: Math.max(key.length, 15) }));
  ws['!cols'] = colWidths;
  
  XLSX.writeFile(wb, `${filename}.xlsx`);
};

// 站点配置
const STATIONS = [
  { id: "JK001", name: "Meruya Ilir Station" }
];

// 创建空的报表数据结构
const createEmptyReportData = (): EODReportData => ({
  report_header: {
    company_name: "PT ANEKA PETROINDO RAYA",
    report_type: "END OF DAY (EOD) REPORT",
    station_id: 1,
    station_name: "Meruya Ilir Station",
    report_date: new Date().toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: '2-digit' })
  },
  shifts: [],
  daily_summary: {
    fuel_grades_summary: [],
    payment_methods_summary: [],
    other_income_summary: []
  },
  allTransactions: []
});

// 从API响应创建前端显示格式的适配器
const createMockDataFromApiResponse = (apiData: any): EODReportData => {
  console.log('=== 开始数据转换 ===');
  console.log('API数据结构:', apiData);
  
  // 创建基础报表数据
  const baseData: EODReportData = {
    report_header: {
      company_name: apiData.report_header?.company_name || "PT ANEKA PETROINDO RAYA",
      report_type: apiData.report_header?.report_type || "END OF DAY (EOD) REPORT",
      station_id: apiData.report_header?.station_id || 1,
      station_name: apiData.report_header?.station_name || "Unknown Station",
      report_date: formatDateToMock(apiData.report_header?.report_date || new Date().toISOString().split('T')[0])
    },
    shifts: [],
    daily_summary: {
      fuel_grades_summary: apiData.daily_summary?.fuel_grades_summary || [],
      payment_methods_summary: apiData.daily_summary?.payment_methods_summary || [],
      other_income_summary: apiData.daily_summary?.other_income_summary || []
    },
    allTransactions: []
  };

  // 转换班次数据
  if (apiData.shifts && apiData.shifts.length > 0) {
    console.log('处理班次数据，共', apiData.shifts.length, '个班次');
    
    baseData.shifts = apiData.shifts.map((shift: any, index: number) => {
      console.log(`处理班次 ${index + 1}:`, shift.shift_info);
      
      const shiftData: ShiftData = {
        shift_number: parseInt(shift.shift_info?.shift_number || (index + 1).toString()),
        shift_name: shift.shift_info?.shift_name || `Shift ${index + 1}`,
        time_range: shift.shift_info?.time_range || "00:00-05:59",
        control_point: shift.shift_summary?.control_point || "normal",
        comments: shift.shift_summary?.comments || "",
        shift_manager: { id: 1, name: "Manager", code: "MGR001" },
        attendants: [],
        allTransactions: [],
        shift_summary: shift.shift_summary ? {
          total_attendants: shift.shift_summary.total_attendants || 0,
          total_transactions: shift.shift_summary.total_transactions || 0,
          total_sales_volume: shift.shift_summary.total_sales_volume || 0,
          total_fuel_sales: shift.shift_summary.total_fuel_sales || 0,
          total_other_income: shift.shift_summary.total_other_income || 0,
          total_cash: shift.shift_summary.total_cash || 0,
          total_non_cash: shift.shift_summary.total_non_cash || 0,
          total_pvc: shift.shift_summary.total_pvc || 0,
          total_cimb: shift.shift_summary.total_cimb || 0,
          total_bca: shift.shift_summary.total_bca || 0,
          total_mandiri: shift.shift_summary.total_mandiri || 0,
          total_bri: shift.shift_summary.total_bri || 0,
          total_bni: shift.shift_summary.total_bni || 0,
          total_voucher: shift.shift_summary.total_voucher || 0,
          total_b2b: shift.shift_summary.total_b2b || 0,
          total_tera: shift.shift_summary.total_tera || 0,
          grand_total: shift.shift_summary.grand_total || 0,
          control_point: shift.shift_summary.control_point || "normal",
          comments: shift.shift_summary.comments || ""
        } : undefined
      };

      // 处理该班次的加油员数据
      if (shift.attendants && shift.attendants.length > 0) {
        console.log(`班次${index + 1}有${shift.attendants.length}个加油员`);
        
        shiftData.attendants = shift.attendants.map((attendant: any) => {
          console.log('处理加油员:', attendant.attendant_info);
          
          const attendantData: AttendantData = {
            employee_id: attendant.attendant_info?.employee_id || 1,
            employee_name: attendant.attendant_info?.attendant_name || "Unknown",
            employee_code: attendant.attendant_info?.employee_code || "UNKNOWN",
            fuel_sales: {
              by_grade: [],
              total: {
                total_volume: 0,
                total_gross_amount: 0,
                total_discount_amount: 0,
                total_net_amount: 0,
                total_transactions: 0
              }
            },
            other_income: {
              nitrogen: attendant.other_income?.items?.find((item: any) => item.item_type === 'nitrogen')?.total_amount || 0,
              other: attendant.other_income?.items?.find((item: any) => item.item_type === 'other')?.total_amount || 0,
              items: attendant.other_income?.items || [],
              total_other_income: attendant.other_income?.total_other_income || 0,
              total_other_transactions: attendant.other_income?.total_other_transactions || 0
            },
            payment_summary: {
              cash: attendant.payment_summary?.cash || 0,
              non_cash_total: attendant.payment_summary?.non_cash_total || 0,
              pvc: attendant.payment_summary?.pvc || 0,
              cimb: attendant.payment_summary?.cimb || 0,
              bca: attendant.payment_summary?.bca || 0,
              mandiri: attendant.payment_summary?.mandiri || 0,
              bri: attendant.payment_summary?.bri || 0,
              bni: attendant.payment_summary?.bni || 0,
              voucher: attendant.payment_summary?.voucher || 0,
              b2b: attendant.payment_summary?.b2b || 0,
              tera: attendant.payment_summary?.tera || 0,
              by_method: attendant.payment_summary?.by_method || []
            },
            allTransactions: []
          };

          // 处理燃油销售数据
          if (attendant.fuel_sales?.by_grade) {
            attendantData.fuel_sales = {
              by_grade: attendant.fuel_sales.by_grade.map((fuel: any) => ({
                fuel_grade: fuel.fuel_grade || "Unknown",
                fuel_name: fuel.fuel_name || fuel.fuel_grade || "Unknown",
                fuel_type: fuel.fuel_type || "",
                sales_volume: fuel.sales_volume || 0,
                free_liters: 0, // API中没有这个字段，保留为兼容性
                discount_amount: fuel.discount_amount || 0,
                net_sales_volume: fuel.sales_volume || 0,
                unit_price: fuel.unit_price || 0,
                sales_amount: fuel.net_amount || fuel.gross_amount || 0,
                gross_amount: fuel.gross_amount || 0,  // 保留毛销售额
                net_amount: fuel.net_amount || 0,      // 保留净销售额
                transaction_count: fuel.transaction_count || 0, // 保留交易笔数
                transactions: [] // 不再生成虚假的交易详情
              })),
              total: attendant.fuel_sales.total || {
                total_volume: 0,
                total_gross_amount: 0,
                total_discount_amount: 0,
                total_net_amount: 0,
                total_transactions: 0
              }
            };
          }

          return attendantData;
        });
      }

      return shiftData;
    });
  }

  // 不再生成虚假的allTransactions，保留空数组以兼容现有接口
  baseData.allTransactions = [];

  console.log('转换完成，最终数据:', baseData);
  return baseData;
};

export default function EndOfDayReportPage() {
  const { t } = useTranslation();
  const [reportData, setReportData] = useState<EODReportData>(createEmptyReportData());
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(() => {
    // 使用雅加达时间获取昨天的日期
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    // 使用 Intl.DateTimeFormat 获取雅加达时区的日期
    const jakartaDateStr = new Intl.DateTimeFormat('en-CA', {
      timeZone: 'Asia/Jakarta',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(yesterday);
    return jakartaDateStr; // 直接返回 YYYY-MM-DD 格式
  });
  const [selectedStation, setSelectedStation] = useState("1");
  
  // 调试：输出站点配置
  console.log("STATIONS configuration:", STATIONS);
  
  // 筛选状态
  const [selectedAttendant, setSelectedAttendant] = useState<string>("ALL");
  const [selectedFuel, setSelectedFuel] = useState<string>("ALL");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>("ALL");
  const [showFilters, setShowFilters] = useState(false);

  const { user, activeRole, activeSite, activeStation, currentSystemType } = useAuth(); // 获取当前用户和上下文
  const isManager = activeRole === 'manager';

  // 如果是manager，直接用其站点信息
  const managerStation = isManager && activeSite
    ? { id: activeSite.siteId, name: activeSite.siteName }
    : null;

  // BOS模式下初始化站点选择
  useEffect(() => {
    console.log('=== End of Day Report 站点初始化 ===');
    console.log('currentSystemType:', currentSystemType);
    console.log('activeStation:', activeStation);
    console.log('activeSite:', activeSite);

    // 只在BOS模式下设置默认站点
    if (currentSystemType !== 'BOS') {
      console.log('非BOS模式，跳过默认站点设置');
      return;
    }

    if (activeStation) {
      console.log('使用activeStation:', activeStation.stationId);
      setSelectedStation(activeStation.stationId.toString());

      // 显示成功提示
      const stationName = activeStation.station?.site_name || 'Unknown Station';
      toast({
        title: "Station Selected",
        description: `Default station set to: ${stationName}`,
        duration: 2000,
      });
    } else if (activeSite) {
      console.log('使用activeSite:', activeSite.siteId);
      setSelectedStation(activeSite.siteId.toString());

      // 显示成功提示
      toast({
        title: "Station Selected",
        description: `Default station set to: ${activeSite.siteName}`,
        duration: 2000,
      });
    } else {
      console.log('使用默认站点ID: 1');
      setSelectedStation("1");

      // 显示警告提示
      toast({
        title: "Default Station Used",
        description: "Using default station. Please verify the selection.",
        variant: "destructive",
        duration: 3000,
      });
    }
  }, [currentSystemType, activeStation, activeSite]);

  // 获取所有可选的筛选选项（从API数据中动态获取）
  const allAttendants = Array.from(new Set(reportData.shifts.flatMap(shift => 
    shift.attendants.map(attendant => attendant.employee_name)
  )));
  const allFuels = Array.from(new Set(reportData.shifts.flatMap(shift =>
    shift.attendants.flatMap(attendant =>
      attendant.fuel_sales.by_grade?.map(fuel => fuel.fuel_name) || []
    )
  )));
  const allPaymentMethods = ['cash', 'cimb', 'bca', 'mandiri', 'bri', 'bni', 'pvc', 'voucher', 'b2b', 'tera'];





  const fetchReport = useCallback(async () => {
    if (!selectedStation || !selectedDate) {
      console.warn('Station or date not selected');
      return;
    }
    
    setIsLoading(true);
    try {
      // 确保station_id是数字类型
      const stationId = parseInt(selectedStation);
      if (isNaN(stationId)) {
        console.error('Invalid station ID:', selectedStation);
        setReportData(createEmptyReportData());
        return;
      }

      // 构建API调用参数，包含筛选条件
      const apiParams: any = {
        station_id: stationId,
        date: selectedDate,
        include_summary: true
      };

      // 添加筛选参数到API调用
      if (selectedAttendant !== "ALL") {
        apiParams.attendant_name = selectedAttendant;
      }
      if (selectedFuel !== "ALL") {
        apiParams.fuel_grade = selectedFuel;
      }
      if (selectedPaymentMethod !== "ALL") {
        apiParams.payment_method = selectedPaymentMethod;
      }

      console.log('=== API调用开始 ===');
      console.log('请求参数:', apiParams);
      console.log('当前用户信息:', { user, activeRole, activeSite, activeStation });
      
      const response = await getEndOfDayReport(apiParams);
      
      console.log('=== API响应 ===');
      console.log('完整响应:', response);
      console.log('响应数据结构:', response?.data);
      console.log('响应成功状态:', response?.success);
      
      // 检查API响应结构
      if (response && response.success && response.data) {
        console.log('API返回成功，开始数据转换');
        const adaptedData = createMockDataFromApiResponse(response.data);
        console.log('转换后的数据:', adaptedData);
        setReportData(adaptedData);
      } else if (response && !response.success) {
        console.warn('API返回失败:', response.message || response.error);
        setReportData(createEmptyReportData());
      } else {
        console.log('API返回空数据或格式不正确');
        setReportData(createEmptyReportData());
      }
    } catch (error) {
      console.error('=== API调用错误 ===');
      console.error('错误详情:', error);
      console.error('错误消息:', error instanceof Error ? error.message : String(error));
      console.error('错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
      // 错误时显示空数据
      setReportData(createEmptyReportData());
    } finally {
      setIsLoading(false);
    }
  }, [selectedStation, selectedDate, selectedAttendant, selectedFuel, selectedPaymentMethod, user, activeRole, activeSite, activeStation]);

  useEffect(() => {
    fetchReport();
  }, [selectedDate, selectedStation, selectedAttendant, selectedFuel, selectedPaymentMethod, fetchReport]);

  const handleExportReport = () => {
    if (!reportData || reportData.shifts.length === 0) {
      alert('No data to export');
      return;
    }

    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 准备数据，按照页面布局结构
    const data: any[][] = [];

    // 1. 报表头部信息
    data.push(['END OF DAY (EOD) REPORT']);
    data.push([]);

    const stationName = (() => {
      if (activeStation) {
        return activeStation.station.site_name;
      } else if (activeSite) {
        return activeSite.siteName;
      } else if (reportData?.report_header?.station_name && reportData.report_header.station_name !== 'Unknown Station') {
        return reportData.report_header.station_name;
      } else {
        return 'Unknown Station';
      }
    })();

    const siteCode = (() => {
      if (activeStation) {
        return activeStation.station.site_code || selectedStation;
      } else if (activeSite) {
        return activeSite.siteName || selectedStation;
      } else {
        return selectedStation;
      }
    })();

    data.push([stationName]);
    data.push([`Site Code: ${siteCode}`]);
    data.push([`Date: ${new Date(selectedDate).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: '2-digit' })}`]);
    data.push([]);

    // 2. 构建表格头部 - 按照页面的复杂表头结构
    // 第一层：空白 + 空白 + 各班次标题
    const headerRow1 = ['', ''];
    reportData.shifts.forEach(shift => {
      // 为每个班次添加列（每个员工一列 + 小计列）
      for (let i = 0; i < shift.attendants.length; i++) {
        headerRow1.push(shift.shift_name);
      }
      headerRow1.push(shift.shift_name + ' Total');
    });
    headerRow1.push('Daily Total');
    data.push(headerRow1);

    // 第二层：空白 + 空白 + 各班次时间
    const headerRow2 = ['', ''];
    reportData.shifts.forEach(shift => {
      for (let i = 0; i < shift.attendants.length; i++) {
        headerRow2.push(shift.time_range);
      }
      headerRow2.push(shift.time_range);
    });
    headerRow2.push('');
    data.push(headerRow2);

    // 第三层：空白 + 空白 + 员工姓名
    const headerRow3 = ['', ''];
    reportData.shifts.forEach(shift => {
      shift.attendants.forEach((attendant: any) => {
        headerRow3.push(attendant.employee_name);
      });
      headerRow3.push('Total');
    });
    headerRow3.push('');
    data.push(headerRow3);

    data.push([]); // 空行分隔

    // 3. 销售量数据
    data.push(['SALES VOLUME (Ltr)', '', ...headerRow3.slice(2)]);

    // BP 92 销售量
    const bp92VolumeRow = ['', 'BP 92'];
    reportData.shifts.forEach(shift => {
      shift.attendants.forEach((attendant: any) => {
        const bp92Data = findFuelData(attendant.fuel_sales, '92');
        bp92VolumeRow.push(bp92Data ? bp92Data.sales_volume.toFixed(3) : '0.000');
      });
      // 班次小计
      const shiftTotal = shift.attendants.reduce((sum: number, att: any) => {
        const bp92 = findFuelData(att.fuel_sales, '92');
        return sum + (bp92?.sales_volume || 0);
      }, 0);
      bp92VolumeRow.push(shiftTotal.toFixed(3));
    });
    // 日汇总
    const dailyTotal = reportData.shifts.reduce((sum: number, shift: any) =>
      sum + shift.attendants.reduce((attSum: number, att: any) => {
        const bp92 = findFuelData(att.fuel_sales, '92');
        return attSum + (bp92?.sales_volume || 0);
      }, 0), 0);
    bp92VolumeRow.push(dailyTotal.toFixed(3));
    data.push(bp92VolumeRow);

    // BP 95 销售量
    const bp95VolumeRow = ['', 'BP Ultimate'];
    reportData.shifts.forEach(shift => {
      shift.attendants.forEach((attendant: any) => {
        const bp95Data = findFuelData(attendant.fuel_sales, '95');
        bp95VolumeRow.push(bp95Data ? bp95Data.sales_volume.toFixed(3) : '0.000');
      });
      // 班次小计
      const shiftTotal = shift.attendants.reduce((sum: number, att: any) => {
        const bp95 = findFuelData(att.fuel_sales, '95');
        return sum + (bp95?.sales_volume || 0);
      }, 0);
      bp95VolumeRow.push(shiftTotal.toFixed(3));
    });
    // 日汇总
    const bp95DailyTotal = reportData.shifts.reduce((sum: number, shift: any) =>
      sum + shift.attendants.reduce((attSum: number, att: any) => {
        const bp95 = findFuelData(att.fuel_sales, '95');
        return attSum + (bp95?.sales_volume || 0);
      }, 0), 0);
    bp95VolumeRow.push(bp95DailyTotal.toFixed(3));
    data.push(bp95VolumeRow);

    // Diesel 销售量
    const dieselVolumeRow = ['', 'BP Diesel'];
    reportData.shifts.forEach(shift => {
      shift.attendants.forEach((attendant: any) => {
        const dieselData = findFuelData(attendant.fuel_sales, 'diesel');
        dieselVolumeRow.push(dieselData ? dieselData.sales_volume.toFixed(3) : '0.000');
      });
      // 班次小计
      const shiftTotal = shift.attendants.reduce((sum: number, att: any) => {
        const diesel = findFuelData(att.fuel_sales, 'diesel');
        return sum + (diesel?.sales_volume || 0);
      }, 0);
      dieselVolumeRow.push(shiftTotal.toFixed(3));
    });
    // 日汇总
    const dieselDailyTotal = reportData.shifts.reduce((sum: number, shift: any) =>
      sum + shift.attendants.reduce((attSum: number, att: any) => {
        const diesel = findFuelData(att.fuel_sales, 'diesel');
        return attSum + (diesel?.sales_volume || 0);
      }, 0), 0);
    dieselVolumeRow.push(dieselDailyTotal.toFixed(3));
    data.push(dieselVolumeRow);

    data.push([]); // 空行分隔

    // 4. 支付方式数据
    data.push(['PAYMENT METHOD (IDR)', '', ...headerRow3.slice(2)]);

    // Cash 支付
    const cashRow = ['', 'Cash'];
    reportData.shifts.forEach(shift => {
      shift.attendants.forEach((attendant: any) => {
        cashRow.push(formatIDR(attendant.payment_summary.cash));
      });
      // 班次小计
      const shiftTotal = shift.attendants.reduce((sum: number, att: any) => sum + att.payment_summary.cash, 0);
      cashRow.push(formatIDR(shiftTotal));
    });
    // 日汇总
    const cashDailyTotal = reportData.shifts.reduce((sum: number, shift: any) =>
      sum + shift.attendants.reduce((attSum: number, att: any) => attSum + att.payment_summary.cash, 0), 0);
    cashRow.push(formatIDR(cashDailyTotal));
    data.push(cashRow);

    // Non-cash Total
    const nonCashRow = ['', 'Non-cash Total'];
    reportData.shifts.forEach(shift => {
      shift.attendants.forEach((attendant: any) => {
        nonCashRow.push(formatIDR(attendant.payment_summary.non_cash_total));
      });
      // 班次小计
      const shiftTotal = shift.attendants.reduce((sum: number, att: any) => sum + att.payment_summary.non_cash_total, 0);
      nonCashRow.push(formatIDR(shiftTotal));
    });
    // 日汇总
    const nonCashDailyTotal = reportData.shifts.reduce((sum: number, shift: any) =>
      sum + shift.attendants.reduce((attSum: number, att: any) => attSum + att.payment_summary.non_cash_total, 0), 0);
    nonCashRow.push(formatIDR(nonCashDailyTotal));
    data.push(nonCashRow);

    // 各银行支付方式
    const bankFields = [
      { name: 'PVC', field: 'pvc' },
      { name: 'CIMB', field: 'cimb' },
      { name: 'BCA', field: 'bca' },
      { name: 'MANDIRI', field: 'mandiri' },
      { name: 'BRI', field: 'bri' },
      { name: 'BNI', field: 'bni' }
    ];

    bankFields.forEach(bank => {
      const bankRow = ['', bank.name];
      reportData.shifts.forEach(shift => {
        shift.attendants.forEach((attendant: any) => {
          bankRow.push(formatIDR(attendant.payment_summary[bank.field] || 0));
        });
        // 班次小计
        const shiftTotal = shift.attendants.reduce((sum: number, att: any) => sum + (att.payment_summary[bank.field] || 0), 0);
        bankRow.push(formatIDR(shiftTotal));
      });
      // 日汇总
      const bankDailyTotal = reportData.shifts.reduce((sum: number, shift: any) =>
        sum + shift.attendants.reduce((attSum: number, att: any) => attSum + (att.payment_summary[bank.field] || 0), 0), 0);
      bankRow.push(formatIDR(bankDailyTotal));
      data.push(bankRow);
    });

    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(data);

    // 设置列宽
    const colWidths = [
      { wch: 20 }, // 第一列
      { wch: 15 }, // 第二列
    ];
    // 为每个数据列设置宽度
    for (let i = 2; i < headerRow1.length; i++) {
      colWidths.push({ wch: 12 });
    }
    ws['!cols'] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "End of Day Report");

    // 下载文件
    const fileName = `end_of_day_report_${selectedDate}_${siteCode}.xlsx`;
    XLSX.writeFile(wb, fileName);
  };



  const handleClearFilters = () => {
    setSelectedAttendant("ALL");
    setSelectedFuel("ALL");
    setSelectedPaymentMethod("ALL");
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <FileText className="mx-auto mb-4 w-12 h-12 animate-pulse text-muted-foreground" />
          <p className="text-muted-foreground">Loading End of Day Report...</p>
          <p className="text-sm text-muted-foreground mt-2">
            Processing station {selectedStation} for {selectedDate}
          </p>
        </div>
      </div>
    );
  }

  // 检查是否有数据
  const hasData = reportData.shifts && reportData.shifts.length > 0;
  const hasAttendants = hasData && reportData.shifts.some(shift => shift.attendants && shift.attendants.length > 0);

  // 计算所有加油员的总数
  const totalAttendants = reportData.shifts.reduce((sum, shift) => sum + shift.attendants.length, 0);
  
  return (
    <div className="max-w-full p-4 space-y-4 bg-gray-50">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900 tracking-tight">End of Day Report</h1>
      </div>

      {/* 筛选条件区域 */}
      <Card className="mb-4 border-gray-300 shadow-sm print:hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium text-gray-900">Filter Conditions</CardTitle>
          <CardDescription className="text-sm text-gray-600">
            Select date and station to generate End of Day Report
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          {/* 基础筛选 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end mb-4">
            <div className="space-y-1.5">
              <label className="text-sm font-medium text-gray-700">Report Date</label>
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full h-9 border-gray-300 focus:border-gray-500"
              />
            </div>

            <div className="space-y-1.5">
              <label className="text-sm font-medium text-gray-700">Station</label>
              <div className="h-9 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700 flex items-center">
                {(() => {
                  if (activeStation) {
                    return activeStation.station.site_name;
                  } else if (activeSite) {
                    return activeSite.siteName;
                  } else if (reportData?.report_header?.station_name && reportData.report_header.station_name !== 'Unknown Station') {
                    return reportData.report_header.station_name;
                  } else {
                    return STATIONS.find(station => station.id === selectedStation)?.name || 'Unknown Station';
                  }
                })()}
              </div>
            </div>

            <div className="space-y-1.5">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="w-full h-9 border-gray-300"
              >
                {showFilters ? 'Hide' : 'Show'} Advanced Filters
              </Button>
            </div>

            <div className="space-y-1.5">
              <div className="flex gap-2">
                <Button onClick={fetchReport} disabled={isLoading} className="h-9 bg-gray-900 hover:bg-gray-800 text-white">
                  {isLoading ? (
                    <RefreshCw className="mr-2 w-4 h-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 w-4 h-4" />
                  )}
                  {isLoading ? 'Querying...' : 'Query'}
                </Button>
                <Button variant="outline" onClick={handleExportReport} className="h-9 border-gray-300">
                  <FileSpreadsheet className="mr-2 w-4 h-4" />
                  Export
                </Button>
              </div>
            </div>
          </div>

          {/* 高级筛选 */}
          {showFilters && (
            <div className="border-t border-gray-200 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {/* 加油员筛选 */}
                <div className="space-y-1.5">
                  <label className="text-sm font-medium text-gray-700">Attendant</label>
                  <Select value={selectedAttendant} onValueChange={setSelectedAttendant}>
                    <SelectTrigger className="h-9 border-gray-300 focus:border-gray-500">
                      <SelectValue placeholder="All Attendants" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Attendants</SelectItem>
                      {allAttendants.map(attendant => (
                        <SelectItem key={attendant} value={attendant}>{attendant}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 油品筛选 */}
                <div className="space-y-1.5">
                  <label className="text-sm font-medium text-gray-700">Fuel Type</label>
                  <Select value={selectedFuel} onValueChange={setSelectedFuel}>
                    <SelectTrigger className="h-9 border-gray-300 focus:border-gray-500">
                      <SelectValue placeholder="All Fuels" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Fuels</SelectItem>
                      {allFuels.map(fuel => (
                        <SelectItem key={fuel} value={fuel}>{fuel}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 支付方式筛选 */}
                <div className="space-y-1.5">
                  <label className="text-sm font-medium text-gray-700">Payment Method</label>
                  <Select value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                    <SelectTrigger className="h-9 border-gray-300 focus:border-gray-500">
                      <SelectValue placeholder="All Methods" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Methods</SelectItem>
                      {allPaymentMethods.map(method => (
                        <SelectItem key={method} value={method}>{method}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 清除筛选按钮 */}
              <div className="mt-4 flex justify-end">
                <Button variant="outline" size="sm" onClick={handleClearFilters} className="border-gray-300">
                  Clear All Filters
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

             {/* 交易详情表格 */}


      {/* 日终报表主体 */}
      <Card className="print:shadow-none print:border-none">
        <CardContent className="p-8">
          {/* 报表头部 */}
          <div className="mb-8 text-center">
            <div className="flex justify-center items-center mb-4">
              <div className="flex justify-center items-center mr-4 w-16 h-16 rounded-full" style={{ backgroundColor: '#00A650' }}>
                <span className="text-xl font-bold text-white">BP</span>
              </div>
              <div className="text-left">
                <h1 className="text-2xl font-bold">{reportData.report_header.company_name}</h1>
                <h2 className="text-xl font-semibold">{reportData.report_header.report_type}</h2>
              </div>
            </div>
            
                         <div className="flex justify-between items-center mt-4 text-sm">
               <div className="text-left">
                 <p><strong>Site Code :</strong> {(() => {
                   if (activeStation) {
                     return activeStation.station.site_code || selectedStation;
                   } else if (activeSite) {
                     return activeSite.siteName || selectedStation;
                   } else {
                     return selectedStation;
                   }
                 })()}</p>
                 <p><strong>Site Name :</strong> {(() => {
                   if (activeStation) {
                     return activeStation.station.site_name;
                   } else if (activeSite) {
                     return activeSite.siteName;
                   } else if (reportData?.report_header?.station_name && reportData.report_header.station_name !== 'Unknown Station') {
                     return reportData.report_header.station_name;
                   } else {
                     return STATIONS.find(station => station.id === selectedStation)?.name || "Unknown Station";
                   }
                 })()}</p>
                 <p><strong>Date :</strong> {new Date(selectedDate).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: '2-digit' })}</p>
               </div>
             </div>
          </div>

          <Separator className="my-6" />

          {/* 全天汇总表 - 类似Shift Report样式 */}
          <div className="w-full mb-6 bg-white border border-gray-300 rounded-lg shadow-sm p-4">
            <div className="text-lg font-medium text-gray-900 mb-3">Daily Summary</div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
              {/* Total Volume */}
              <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
                <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">
                  {(() => {
                    // 使用后端返回的总油量数据
                    const totalVolume = (reportData.daily_summary as any)?.total_fuel_volume;
                    if (totalVolume !== undefined && totalVolume !== null) {
                      return formatVolume(totalVolume);
                    }
                    // 备用计算：从fuel_grades_summary汇总
                    const calculatedVolume = reportData.daily_summary?.fuel_grades_summary?.reduce((sum: number, fuel: any) =>
                      sum + (fuel.total_volume || 0), 0) || 0;
                    return formatVolume(calculatedVolume);
                  })()}
                </div>
                <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
                  <span>TOTAL/VOLUME (L)</span>
                </div>
              </div>

              {/* Total Sales */}
              <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
                <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">
                  {(() => {
                    // 使用净销售额（去除折扣后）
                    const netSales = (reportData.daily_summary as any)?.total_net_sales;
                    if (netSales !== undefined) {
                      return formatIDR(netSales);
                    }
                    // 备用计算：从fuel_grades_summary汇总净销售额
                    const calculatedNet = reportData.daily_summary?.fuel_grades_summary?.reduce((sum: number, fuel: any) =>
                      sum + (fuel.total_net_amount || 0), 0) || 0;
                    return formatIDR(calculatedNet);
                  })()}
                </div>
                <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
                  <span>TOTAL/SALES (IDR)</span>
                </div>
              </div>

              {/* Free Liters */}
              <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
                <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">
                  {formatVolume(
                    reportData.shifts.reduce((sum: number, shift: any) =>
                      sum + shift.attendants.reduce((attSum: number, att: any) =>
                        attSum + (att.fuel_sales.by_grade?.reduce((fuelSum: number, fuel: any) =>
                          fuelSum + (fuel.free_liters || 0), 0) || 0), 0), 0)
                  )}
                </div>
                <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
                  <span>FREE/LITERS (L)</span>
                </div>
              </div>

              {/* Total Discount */}
              <div className="border border-gray-300 rounded-md p-3 flex flex-col items-center justify-center bg-white">
                <div className="text-lg font-semibold text-gray-900 mb-1 font-mono tabular-nums">
                  {(() => {
                    // 优先使用后端汇总数据
                    const backendDiscount = (reportData.daily_summary as any)?.total_discount;
                    if (backendDiscount !== undefined && backendDiscount > 0) {
                      return formatIDR(backendDiscount);
                    }
                    // 备用前端计算（因为后端可能返回0）
                    const frontendTotal = reportData.daily_summary?.fuel_grades_summary?.reduce((sum: number, fuel: any) =>
                      sum + (fuel.total_discount_amount || 0), 0) || 0;
                    return formatIDR(frontendTotal);
                  })()}
                </div>
                <div className="text-xs font-medium text-gray-600 text-center uppercase tracking-wide">
                  <span>TOTAL/DISCOUNT (IDR)</span>
                </div>
              </div>
            </div>
          </div>

          {/* 整合的4班次透视表 */}
          <Card className="border-gray-300 shadow-sm mb-6">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium text-gray-900">
                Daily Shift Summary - All Shifts
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">

              <div className="overflow-x-auto">

                <style>{`
                  .eod-table th:first-child,
                  .eod-table td:first-child {
                    min-width: 140px;
                    max-width: 140px;
                  }
                  .eod-table th:nth-child(2),
                  .eod-table td:nth-child(2) {
                    min-width: 84px;
                    max-width: 84px;
                  }
                `}</style>
                <Table className="border border-gray-400 border-collapse eod-table bg-white">
                  <TableHeader>
                    {/* 第一层表头：班次编号 */}
                    <TableRow>
                      <TableHead 
                        colSpan={2}
                        className="sticky left-0 z-10 font-bold text-center bg-white border border-gray-400"
                        style={{ width: '224px' }}
                      >
                        Shift
                      </TableHead>
                      {reportData.shifts.map((shift, shiftIndex) => (
                        <TableHead
                          key={shift.shift_number}
                          colSpan={shift.attendants.length + 1}
                          className="text-center border border-gray-400 font-bold bg-gray-100 text-gray-900"
                        >
                          {shift.shift_name}
                        </TableHead>
                      ))}
                      <TableHead className="text-center bg-gray-100 border border-gray-400 min-w-32 font-bold">
                        Daily Total
                      </TableHead>
                    </TableRow>
                    
                    {/* 第二层表头：班次时间 */}
                    <TableRow>
                      <TableHead 
                        colSpan={2}
                        className="sticky left-0 z-10 font-bold text-center bg-white border border-gray-300"
                        style={{ width: '224px' }}
                      >
                        Shift Hour
                      </TableHead>
                      {reportData.shifts.map((shift, shiftIndex) => (
                        <TableHead
                          key={`${shift.shift_number}-time`}
                          colSpan={shift.attendants.length + 1}
                          className="text-center border border-gray-400 font-medium bg-gray-50 text-gray-700"
                        >
                          {shift.time_range}
                        </TableHead>
                      ))}
                      <TableHead className="text-center bg-gray-100 border border-gray-300 min-w-32 font-bold">
                        
                      </TableHead>
                    </TableRow>
                    
                    {/* 第三层表头：加油员信息 */}
                    <TableRow style={{ height: 32 }}>
                      <TableHead 
                        colSpan={2}
                        className="sticky left-0 z-20 font-bold text-center bg-white border border-gray-300"
                        style={{ width: '224px', height: 32, paddingTop: 2, paddingBottom: 2 }}
                      >
                        Fuel Attendant
                      </TableHead>
                      {reportData.shifts.map((shift, shiftIndex) => (
                        <React.Fragment key={`shift-${shift.shift_number}`}>
                          {shift.attendants.map((attendant: any, attendantIndex) => (
                            <TableHead
                              key={`${shift.shift_number}-${attendant.employee_id}`}
                              className="text-center border border-gray-400 min-w-32 bg-white"
                              style={{
                                height: 32,
                                paddingTop: 2,
                                paddingBottom: 2
                              }}
                            >
                              <div className="space-y-1">
                                <div className="text-sm font-medium text-gray-900" style={{ lineHeight: '1.2' }}>{attendant.employee_name}</div>
                              </div>
                            </TableHead>
                          ))}
                          <TableHead
                            className="text-center border border-gray-400 min-w-32 font-bold bg-gray-100"
                            style={{
                              height: 32,
                              paddingTop: 2,
                              paddingBottom: 2
                            }}
                          >
                            <div className="space-y-1">
                              <div className="text-xs text-gray-900" style={{ lineHeight: '1.2' }}>Total</div>
                            </div>
                          </TableHead>
                        </React.Fragment>
                      ))}
                      <TableHead className="text-center bg-gray-100 border border-gray-300 min-w-32 font-bold" style={{ height: 32, paddingTop: 2, paddingBottom: 2 }}>
                        {/* Daily-Total */}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  
                  <TableBody>
                    {/* 1. Sales Volume (Ltr) 分组 */}
                    <TableRow>
                      <TableCell 
                        rowSpan={3} 
                        className="sticky left-0 z-10 font-semibold bg-gray-50 border border-gray-300 align-middle text-center"
                        style={{ minWidth: '140px', maxWidth: '140px' }}
                      >
                        Sales Volume (Ltr)
                      </TableCell>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#22C55E', color: '#FFFFFF', left: '140px', minWidth: '84px', maxWidth: '84px' }}>BP 92</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp92`}>
                          {shift.attendants.map((attendant: any) => {
                            const bp92Data = findFuelData(attendant.fuel_sales, '92');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-92`} className="text-right border border-gray-300">
                                {bp92Data ? formatVolume(bp92Data.sales_volume) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const bp92Summary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP 92" && f.fuel_type === "101"
                              );
                              if (bp92Summary) {
                                return formatVolume(bp92Summary.total_volume);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const bp92Data = findFuelData(att.fuel_sales, '92');
                                return sum + (bp92Data?.sales_volume || 0);
                              }, 0);
                              return total > 0 ? formatVolume(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const bp92Summary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP 92" && f.fuel_type === "101"
                          );
                          return bp92Summary ? formatVolume(bp92Summary.total_volume) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                    
                    {/* BP Ultimate 行 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#F97316', color: '#FFFFFF', left: '140px' }}>Ultimate</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp95`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpUltimateData = findFuelData(attendant.fuel_sales, '95');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-95`} className="text-right border border-gray-300">
                                {bpUltimateData ? formatVolume(bpUltimateData.sales_volume) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const ultimateSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                              );
                              if (ultimateSummary) {
                                return formatVolume(ultimateSummary.total_gross_amount / ultimateSummary.average_price);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const ultimateData = findFuelData(att.fuel_sales, '95');
                                return sum + (ultimateData?.sales_volume || 0);
                              }, 0);
                              return total > 0 ? formatVolume(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const ultimateSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                          );
                          return ultimateSummary ? formatVolume(ultimateSummary.total_gross_amount / ultimateSummary.average_price) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                    
                    {/* BP Diesel 行 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#374151', color: '#FFFFFF', left: '140px' }}>Diesel</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bpdiesel`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpDieselData = findFuelData(attendant.fuel_sales, 'diesel');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-diesel`} className="text-right border border-gray-300">
                                {bpDieselData ? formatVolume(bpDieselData.sales_volume) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const dieselSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                              );
                              if (dieselSummary) {
                                return formatVolume(dieselSummary.total_gross_amount / dieselSummary.average_price);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const dieselData = findFuelData(att.fuel_sales, 'diesel');
                                return sum + (dieselData?.sales_volume || 0);
                              }, 0);
                              return total > 0 ? formatVolume(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const dieselSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                          );
                          return dieselSummary ? formatVolume(dieselSummary.total_gross_amount / dieselSummary.average_price) : '-';
                        })()}
                      </TableCell>
                    </TableRow>

                    {/* 2. Free Liter 分组 */}
                    <TableRow>
                      <TableCell 
                        rowSpan={3} 
                        className="sticky left-0 z-10 font-semibold bg-gray-50 border border-gray-300 align-middle text-center"
                      >
                        Free Liter
                      </TableCell>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#22C55E', color: '#FFFFFF', left: '140px' }}>BP 92</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp92-free`}>
                          {shift.attendants.map((attendant: any) => {
                            const bp92Data = findFuelData(attendant.fuel_sales, '92');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-92-free`} className="text-right border border-gray-300">
                                {bp92Data ? formatVolume(bp92Data.free_liters || 0) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {formatVolume(
                              shift.attendants.reduce((sum: number, att: any) => {
                                const bp92 = findFuelData(att.fuel_sales, '92');
                                return sum + (bp92?.free_liters || 0);
                              }, 0)
                            )}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {formatVolume(
                          reportData.shifts.reduce((sum: number, shift: any) => 
                            sum + shift.attendants.reduce((attSum: number, att: any) => {
                              const bp92 = findFuelData(att.fuel_sales, '92');
                              return attSum + (bp92?.free_liters || 0);
                            }, 0), 0)
                        )}
                      </TableCell>
                    </TableRow>
                    
                    {/* Ultimate Free Liter 行 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#F97316', color: '#FFFFFF', left: '140px' }}>Ultimate</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp95-free`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpUltimateData = findFuelData(attendant.fuel_sales, '95');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-95-free`} className="text-right border border-gray-300">
                                {bpUltimateData ? formatVolume(bpUltimateData.free_liters) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {formatVolume(
                              shift.attendants.reduce((sum: number, att: any) => {
                                const bpUltimate = findFuelData(att.fuel_sales, '95');
                                return sum + (bpUltimate?.free_liters || 0);
                              }, 0)
                            )}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {formatVolume(
                          reportData.shifts.reduce((sum: number, shift: any) => 
                            sum + shift.attendants.reduce((attSum: number, att: any) => {
                              const bpUltimate = findFuelData(att.fuel_sales, '95');
                              return attSum + (bpUltimate?.free_liters || 0);
                            }, 0), 0)
                        )}
                      </TableCell>
                    </TableRow>
                    
                    {/* Diesel Free Liter 行 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#374151', color: '#FFFFFF', left: '140px' }}>Diesel</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-diesel-free`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpDieselData = findFuelData(attendant.fuel_sales, 'diesel');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-diesel-free`} className="text-right border border-gray-300">
                                {bpDieselData ? formatVolume(bpDieselData.free_liters) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {formatVolume(
                              shift.attendants.reduce((sum: number, att: any) => {
                                const bpDiesel = findFuelData(att.fuel_sales, 'diesel');
                                return sum + (bpDiesel?.free_liters || 0);
                              }, 0)
                            )}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {formatVolume(
                          reportData.shifts.reduce((sum: number, shift: any) => 
                            sum + shift.attendants.reduce((attSum: number, att: any) => {
                              const bpDiesel = findFuelData(att.fuel_sales, 'diesel');
                              return attSum + (bpDiesel?.free_liters || 0);
                            }, 0), 0)
                        )}
                      </TableCell>
                    </TableRow>

                    {/* 3. Discount Amount 分组 */}
                    <TableRow>
                      <TableCell 
                        rowSpan={3} 
                        className="sticky left-0 z-10 font-semibold bg-gray-50 border border-gray-300 align-middle text-center"
                      >
                        Discount Amount (IDR)
                      </TableCell>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#22C55E', color: '#FFFFFF', left: '140px' }}>BP 92</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp92-discount`}>
                          {shift.attendants.map((attendant: any) => {
                            const bp92Data = findFuelData(attendant.fuel_sales, '92');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-92-discount`} className="text-right border border-gray-300">
                                {bp92Data ? formatIDR(bp92Data.discount_amount) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const bp92Summary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP 92" && f.fuel_type === "101"
                              );
                              if (bp92Summary) {
                                return formatIDR(bp92Summary.total_discount_amount);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const bp92Data = findFuelData(att.fuel_sales, '92');
                                return sum + (bp92Data?.discount_amount || 0);
                              }, 0);
                              return total > 0 ? formatIDR(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const bp92Summary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP 92" && f.fuel_type === "101"
                          );
                          return bp92Summary ? formatIDR(bp92Summary.total_discount_amount) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                    
                    {/* Ultimate Discount Amount 行 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#F97316', color: '#FFFFFF', left: '140px' }}>Ultimate</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp95-discount`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpUltimateData = findFuelData(attendant.fuel_sales, '95');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-95-discount`} className="text-right border border-gray-300">
                                {bpUltimateData ? formatIDR(bpUltimateData.discount_amount) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const ultimateSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                              );
                              if (ultimateSummary) {
                                return formatIDR(ultimateSummary.total_discount_amount);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const ultimateData = findFuelData(att.fuel_sales, '95');
                                return sum + (ultimateData?.discount_amount || 0);
                              }, 0);
                              return total > 0 ? formatIDR(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const ultimateSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                          );
                          return ultimateSummary ? formatIDR(ultimateSummary.total_discount_amount) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                    
                    {/* Diesel Discount Amount 行 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#374151', color: '#FFFFFF', left: '140px' }}>Diesel</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-diesel-discount`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpDieselData = findFuelData(attendant.fuel_sales, 'diesel');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-diesel-discount`} className="text-right border border-gray-300">
                                {bpDieselData ? formatIDR(bpDieselData.discount_amount) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const dieselSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                              );
                              if (dieselSummary) {
                                return formatIDR(dieselSummary.total_discount_amount);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const dieselData = findFuelData(att.fuel_sales, 'diesel');
                                return sum + (dieselData?.discount_amount || 0);
                              }, 0);
                              return total > 0 ? formatIDR(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const dieselSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                          );
                          return dieselSummary ? formatIDR(dieselSummary.total_discount_amount) : '-';
                        })()}
                      </TableCell>
                    </TableRow>

                    {/* 4. Net Sales Volume (Ltr) - 3个油类 */}
                    <TableRow>
                      <TableCell rowSpan={3} className="sticky left-0 z-10 font-semibold bg-gray-50 border border-gray-300 align-middle text-center">Net Sales Volume (Ltr)</TableCell>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#22C55E', color: '#FFFFFF', left: '140px' }}>BP 92</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp92-net`}> 
                          {shift.attendants.map((attendant: any) => {
                            const bp92Data = findFuelData(attendant.fuel_sales, '92');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-92-net`} className="text-right border border-gray-300">
                                {bp92Data ? formatVolume(bp92Data.net_sales_volume) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const bp92Summary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP 92" && f.fuel_type === "101"
                              );
                              if (bp92Summary) {
                                return formatVolume(bp92Summary.total_volume);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const bp92Data = findFuelData(att.fuel_sales, '92');
                                return sum + (bp92Data?.net_sales_volume || 0);
                              }, 0);
                              return total > 0 ? formatVolume(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const bp92Summary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP 92" && f.fuel_type === "101"
                          );
                          return bp92Summary ? formatVolume(bp92Summary.total_volume) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                    {/* Ultimate */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#F97316', color: '#FFFFFF', left: '140px' }}>Ultimate</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp95-net`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpUltimateData = findFuelData(attendant.fuel_sales, '95');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-95-net`} className="text-right border border-gray-300">
                                {bpUltimateData ? formatVolume(bpUltimateData.net_sales_volume) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const ultimateSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                              );
                              if (ultimateSummary) {
                                return formatVolume(ultimateSummary.total_volume);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const ultimateData = findFuelData(att.fuel_sales, '95');
                                return sum + (ultimateData?.net_sales_volume || 0);
                              }, 0);
                              return total > 0 ? formatVolume(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const ultimateSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                          );
                          return ultimateSummary ? formatVolume(ultimateSummary.total_volume) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                    {/* Diesel */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#374151', color: '#FFFFFF', left: '140px' }}>Diesel</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bpdiesel-net`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpDieselData = findFuelData(attendant.fuel_sales, 'diesel');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-diesel-net`} className="text-right border border-gray-300">
                                {bpDieselData ? formatVolume(bpDieselData.net_sales_volume) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const dieselSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                              );
                              if (dieselSummary) {
                                return formatVolume(dieselSummary.total_volume);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const dieselData = findFuelData(att.fuel_sales, 'diesel');
                                return sum + (dieselData?.net_sales_volume || 0);
                              }, 0);
                              return total > 0 ? formatVolume(total) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const dieselSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                          );
                          return dieselSummary ? formatVolume(dieselSummary.total_volume) : '-';
                        })()}
                      </TableCell>
                    </TableRow>

                    {/* 5. Price (IDR) - 3个油类 */}
                    <TableRow>
                      <TableCell rowSpan={3} className="sticky left-0 z-10 font-semibold bg-gray-50 border border-gray-300 align-middle text-center">Price (IDR)</TableCell>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#22C55E', color: '#FFFFFF', left: '140px' }}>BP 92</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp92-price`}>
                          {shift.attendants.map((attendant: any) => {
                            const bp92Data = findFuelData(attendant.fuel_sales, '92');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-92-price`} className="text-right border border-gray-300">
                                {bp92Data ? formatIDR(bp92Data.unit_price) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const bp92Summary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP 92" && f.fuel_type === "101"
                              );
                              if (bp92Summary) {
                                return formatIDR(Math.round(bp92Summary.average_price));
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const totalAmount = shift.attendants.reduce((sum: number, att: any) => {
                                const bp92Data = findFuelData(att.fuel_sales, '92');
                                return sum + (bp92Data?.gross_amount || 0);
                              }, 0);
                              const totalVolume = shift.attendants.reduce((sum: number, att: any) => {
                                const bp92Data = findFuelData(att.fuel_sales, '92');
                                return sum + (bp92Data?.sales_volume || 0);
                              }, 0);
                              return totalVolume > 0 ? formatIDR(Math.round(totalAmount / totalVolume)) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const bp92Summary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP 92" && f.fuel_type === "101"
                          );
                          return bp92Summary ? formatIDR(Math.round(bp92Summary.average_price)) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                    {/* Ultimate Price */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#F97316', color: '#FFFFFF', left: '140px' }}>Ultimate</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp95-price`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpUltimateData = findFuelData(attendant.fuel_sales, '95');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-95-price`} className="text-right border border-gray-300">
                                {bpUltimateData ? formatIDR(bpUltimateData.unit_price) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const ultimateSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                              );
                              if (ultimateSummary) {
                                return formatIDR(Math.round(ultimateSummary.average_price));
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const ultimateSales = shift.attendants.filter((att: any) => {
                                const ultimate = findFuelData(att.fuel_sales, '95');
                                return ultimate && ultimate.sales_volume > 0;
                              });
                              if (ultimateSales.length === 0) return '-';

                              const totalAmount = ultimateSales.reduce((sum: number, att: any) => {
                                const ultimate = findFuelData(att.fuel_sales, '95');
                                return sum + (ultimate?.gross_amount || 0);
                              }, 0);
                              const totalVolume = ultimateSales.reduce((sum: number, att: any) => {
                                const ultimate = findFuelData(att.fuel_sales, '95');
                                return sum + (ultimate?.sales_volume || 0);
                              }, 0);

                              return totalVolume > 0 ? formatIDR(Math.round(totalAmount / totalVolume)) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const ultimateSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                          );
                          return ultimateSummary ? formatIDR(Math.round(ultimateSummary.average_price)) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                    {/* Diesel Price */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#374151', color: '#FFFFFF', left: '140px' }}>Diesel</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-diesel-price`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpDieselData = findFuelData(attendant.fuel_sales, 'diesel');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-diesel-price`} className="text-right border border-gray-300">
                                {bpDieselData ? formatIDR(bpDieselData.unit_price) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const dieselSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                              );
                              if (dieselSummary) {
                                return formatIDR(Math.round(dieselSummary.average_price));
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const dieselSales = shift.attendants.filter((att: any) => {
                                const diesel = findFuelData(att.fuel_sales, 'diesel');
                                return diesel && diesel.sales_volume > 0;
                              });
                              if (dieselSales.length === 0) return '-';

                              const totalAmount = dieselSales.reduce((sum: number, att: any) => {
                                const diesel = findFuelData(att.fuel_sales, 'diesel');
                                return sum + (diesel?.gross_amount || 0);
                              }, 0);
                              const totalVolume = dieselSales.reduce((sum: number, att: any) => {
                                const diesel = findFuelData(att.fuel_sales, 'diesel');
                                return sum + (diesel?.sales_volume || 0);
                              }, 0);

                              return totalVolume > 0 ? formatIDR(Math.round(totalAmount / totalVolume)) : '-';
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const dieselSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                          );
                          return dieselSummary ? formatIDR(Math.round(dieselSummary.average_price)) : '-';
                        })()}
                      </TableCell>
                    </TableRow>

                    {/* 6. Sales Amount (IDR) 分组 */}
                    <TableRow>
                      <TableCell 
                        rowSpan={3} 
                        className="sticky left-0 z-10 font-semibold bg-gray-50 border border-gray-300 align-middle text-center"
                      >
                        Sales Amount (IDR)
                      </TableCell>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#22C55E', color: '#FFFFFF', left: '140px' }}>BP 92</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp92-amount`}>
                          {shift.attendants.map((attendant: any) => {
                            const bp92Data = findFuelData(attendant.fuel_sales, '92');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-92-amount`} className="text-right border border-gray-300">
                                {bp92Data ? formatIDR(bp92Data.net_amount || bp92Data.sales_amount || 0) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const bp92Summary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP 92" && f.fuel_type === "101"
                              );
                              if (bp92Summary) {
                                return formatIDR(bp92Summary.total_net_amount);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const bp92 = findFuelData(att.fuel_sales, '92');
                                return sum + (bp92?.net_amount || 0);
                              }, 0);
                              return formatIDR(total);
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const bp92Summary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP 92" && f.fuel_type === "101"
                          );
                          return bp92Summary ? formatIDR(bp92Summary.total_net_amount) : formatIDR(0);
                        })()}
                      </TableCell>
                    </TableRow>
                    
                    {/* BP Ultimate 销售额 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#F97316', color: '#FFFFFF', left: '140px' }}>Ultimate</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bp95-amount`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpUltimateData = findFuelData(attendant.fuel_sales, '95');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-95-amount`} className="text-right border border-gray-300">
                                {bpUltimateData ? formatIDR(bpUltimateData.net_amount) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const ultimateSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                              );
                              if (ultimateSummary) {
                                return formatIDR(ultimateSummary.total_net_amount);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const bpUltimate = findFuelData(att.fuel_sales, '95');
                                return sum + (bpUltimate?.net_amount || 0);
                              }, 0);
                              return formatIDR(total);
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const ultimateSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate" && f.fuel_type === "102"
                          );
                          return ultimateSummary ? formatIDR(ultimateSummary.total_net_amount) : formatIDR(0);
                        })()}
                      </TableCell>
                    </TableRow>
                    
                    {/* BP Diesel 销售额 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#374151', color: '#FFFFFF', left: '140px' }}>Diesel</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-bpdiesel-amount`}>
                          {shift.attendants.map((attendant: any) => {
                            const bpDieselData = findFuelData(attendant.fuel_sales, 'diesel');
                            return (
                              <TableCell key={`${shift.shift_number}-${attendant.employee_id}-diesel-amount`} className="text-right border border-gray-300">
                                {bpDieselData ? formatIDR(bpDieselData.net_amount) : '-'}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {(() => {
                              // 优先使用后端返回的班次汇总数据
                              const dieselSummary = shift.shift_summary?.fuel_grades_summary?.find((f: any) =>
                                f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                              );
                              if (dieselSummary) {
                                return formatIDR(dieselSummary.total_net_amount);
                              }
                              // 如果后端没有数据，前端根据员工数据计算
                              const total = shift.attendants.reduce((sum: number, att: any) => {
                                const bpDiesel = findFuelData(att.fuel_sales, 'diesel');
                                return sum + (bpDiesel?.net_amount || 0);
                              }, 0);
                              return formatIDR(total);
                            })()}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {(() => {
                          // 使用后端返回的日汇总数据
                          const dieselSummary = reportData.daily_summary?.fuel_grades_summary?.find((f: any) =>
                            f.fuel_grade === "BP Ultimate Diesel" && f.fuel_type === "103"
                          );
                          return dieselSummary ? formatIDR(dieselSummary.total_net_amount) : formatIDR(0);
                        })()}
                      </TableCell>
                    </TableRow>

                    {/* 7. Total Sales Fuel - 淡黄色 */}
                    <TableRow>
                      <TableCell colSpan={2} className="sticky left-0 z-10 font-semibold border border-gray-300 align-middle text-center" style={{ backgroundColor: '#FFF9E6' }}>Total Sales Fuel</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-total-sales-fuel-cols`}>
                          {shift.attendants.map((attendant: any) => (
                            <TableCell key={`attendant-${attendant.employee_id}-total-sales-fuel`} className="text-right border border-gray-300" style={{ backgroundColor: '#FFF9E6' }}>
                              {formatIDR(attendant.fuel_sales.by_grade?.reduce((sum: number, fuel: any) => sum + (fuel.net_amount || 0), 0) || 0)}
                            </TableCell>
                          ))}
                          <TableCell className="text-right border border-gray-300 font-semibold" style={{ backgroundColor: '#FFF9E6' }}>
                            {formatIDR(shift.shift_summary?.total_fuel_sales || 0)}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right border border-gray-300 font-bold" style={{ backgroundColor: '#FFF9E6' }}>
                        {formatIDR(
                          reportData.shifts.reduce((sum: number, shift: any) =>
                            sum + (shift.shift_summary?.total_fuel_sales || 0), 0)
                        )}
                      </TableCell>
                    </TableRow>

                    {/* 8. Other Income (IDR) 分组 */}
                    <TableRow>
                      <TableCell 
                        rowSpan={2} 
                        className="sticky left-0 z-10 font-semibold bg-gray-50 border border-gray-300 align-middle text-center"
                      >
                        Other Income (IDR)
                      </TableCell>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#4A90E2', color: '#FFFFFF', left: '140px' }}>Nitrogen</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-nitrogen`}>
                          {shift.attendants.map((attendant: any) => (
                            <TableCell key={`${shift.shift_number}-${attendant.employee_id}-nitrogen`} className="text-right border border-gray-300">
                              {formatIDR(attendant.other_income.nitrogen)}
                            </TableCell>
                          ))}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {formatIDR(
                              shift.attendants.reduce((sum: number, att: any) => sum + att.other_income.nitrogen, 0)
                            )}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {formatIDR(
                          reportData.shifts.reduce((sum: number, shift: any) => 
                            sum + shift.attendants.reduce((attSum: number, att: any) => attSum + att.other_income.nitrogen, 0), 0)
                        )}
                      </TableCell>
                    </TableRow>
                    
                    {/* Other Income - Other 行 */}
                    <TableRow>
                      <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#9B59B6', color: '#FFFFFF', left: '140px' }}>Other</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-other`}>
                          {shift.attendants.map((attendant: any) => (
                            <TableCell key={`${shift.shift_number}-${attendant.employee_id}-other`} className="text-right border border-gray-300">
                              {formatIDR(attendant.other_income.other)}
                            </TableCell>
                          ))}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {formatIDR(
                              shift.attendants.reduce((sum: number, att: any) => sum + att.other_income.other, 0)
                            )}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {formatIDR(
                          reportData.shifts.reduce((sum: number, shift: any) => 
                            sum + shift.attendants.reduce((attSum: number, att: any) => attSum + att.other_income.other, 0), 0)
                        )}
                      </TableCell>
                    </TableRow>

                    {/* 9. Total Other Income - 淡黄色 */}
                    <TableRow>
                      <TableCell colSpan={2} className="sticky left-0 z-10 font-semibold border border-gray-300 align-middle text-center" style={{ backgroundColor: '#FFF9E6' }}>Total Other Income</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-total-other-income-cols`}>
                          {shift.attendants.map((attendant: any) => (
                            <TableCell key={`attendant-${attendant.employee_id}-total-other-income`} className="text-right border border-gray-300" style={{ backgroundColor: '#FFF9E6' }}>
                              {formatIDR(attendant.other_income.nitrogen + attendant.other_income.other)}
                            </TableCell>
                          ))}
                          <TableCell className="text-right border border-gray-300 font-semibold" style={{ backgroundColor: '#FFF9E6' }}>
                            {formatIDR(shift.shift_summary?.total_other_income || 0)}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right border border-gray-300 font-semibold" style={{ backgroundColor: '#FFF9E6' }}>
                        {formatIDR(
                          reportData.shifts.reduce((sum: number, shift: any) =>
                            sum + (shift.shift_summary?.total_other_income || 0), 0)
                        )}
                      </TableCell>
                    </TableRow>

                    {/* 9.5. Fuel Attendant - 白色背景，小行高 */}
                    <TableRow className="h-8">
                      <TableCell colSpan={2} className="sticky left-0 z-10 font-semibold border border-gray-300 align-middle text-center bg-white">Fuel Attendant</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-fuel-attendant-cols`}>
                          {shift.attendants.map((attendant: any) => {
                            // 简化员工姓名显示
                            const formatEmployeeName = (fullName: string): string => {
                              const nameParts = fullName.trim().split(' ');
                              if (nameParts.length >= 2) {
                                const firstName = nameParts[0];
                                const lastName = nameParts[nameParts.length - 1];
                                return `${firstName.charAt(0)}. ${lastName}`;
                              }
                              return fullName;
                            };
                            
                            return (
                              <TableCell key={`attendant-${attendant.employee_id}-fuel-attendant`} className="text-center border border-gray-300 bg-white">
                                {formatEmployeeName(attendant.employee_name)}
                              </TableCell>
                            );
                          })}
                          <TableCell className="text-center border border-gray-300 font-semibold bg-white">
                            Shift Total
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-center border border-gray-300 font-bold bg-white">
                        Total
                      </TableCell>
                    </TableRow>

                    {/* 10. Grand Total - 橙黄色 */}
                    <TableRow>
                      <TableCell colSpan={2} className="sticky left-0 z-10 font-semibold border border-gray-300 align-middle text-center" style={{ backgroundColor: '#FFE6CC' }}>Grand Total</TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-grand-total-cols`}>
                          {shift.attendants.map((attendant: any) => (
                            <TableCell key={`attendant-${attendant.employee_id}-grand-total`} className="text-right border border-gray-300" style={{ backgroundColor: '#FFE6CC' }}>
                              {formatIDR(
                                (attendant.fuel_sales.by_grade?.reduce((sum: number, fuel: any) => sum + (fuel.net_amount || 0), 0) || 0) +
                                (attendant.other_income?.nitrogen || 0) + (attendant.other_income?.other || 0)
                              )}
                            </TableCell>
                          ))}
                          <TableCell className="text-right border border-gray-300 font-semibold" style={{ backgroundColor: '#FFE6CC' }}>
                            {formatIDR(shift.shift_summary?.grand_total || 0)}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right border border-gray-300 font-bold" style={{ backgroundColor: '#FFE6CC' }}>
                        {formatIDR(
                          reportData.shifts.reduce((sum: number, shift: any) =>
                            sum + (shift.shift_summary?.grand_total || 0), 0)
                        )}
                      </TableCell>
                    </TableRow>

                    {/* 11. Payment Method (IDR) 分组 */}
                    {/* Cash 行 */}
                    <TableRow>
                      <TableCell colSpan={2} className="sticky left-0 z-10 font-semibold border border-gray-300 align-middle text-center" style={{ backgroundColor: '#28A745', color: '#FFFFFF' }}>
                        Cash
                      </TableCell>
                      {reportData.shifts.map((shift) => (
                        <React.Fragment key={`shift-${shift.shift_number}-cash`}>
                          {shift.attendants.map((attendant: any) => (
                            <TableCell key={`${shift.shift_number}-${attendant.employee_id}-cash`} className="text-right border border-gray-300">
                              {formatIDR(attendant.payment_summary.cash)}
                            </TableCell>
                          ))}
                          <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                            {formatIDR(shift.shift_summary?.total_cash || 0)}
                          </TableCell>
                        </React.Fragment>
                      ))}
                      <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                        {formatIDR(
                          reportData.shifts.reduce((sum: number, shift: any) =>
                            sum + (shift.shift_summary?.total_cash || 0), 0)
                        )}
                      </TableCell>
                    </TableRow>

                    {/* Non-cash 区域 2列7行，第一列合并，第二列为 Non-cash Total + 6个支付方式 */}
                    {(() => {
                      // 使用真实的银行支付方式数据，而不是假的百分比分配
                      const bankFields = [
                        { name: 'PVC', field: 'pvc' },
                        { name: 'CIMB', field: 'cimb' },
                        { name: 'BCA', field: 'bca' },
                        { name: 'MANDIRI', field: 'mandiri' },
                        { name: 'BRI', field: 'bri' },
                        { name: 'BNI', field: 'bni' }
                      ];
                      // Non-cash Total 行
                      const rows = [
                        <TableRow key="non-cash-total">
                          <TableCell rowSpan={7} className="sticky left-0 z-10 font-semibold border border-gray-300 align-middle text-center" style={{ backgroundColor: '#6F42C1', color: '#FFFFFF' }}>
                            Non-cash
                          </TableCell>
                          <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#6F42C1', color: '#FFFFFF', left: '140px' }}>Non-cash Total</TableCell>
                          {reportData.shifts.map((shift) => (
                            <React.Fragment key={`shift-${shift.shift_number}-noncash-total`}>
                              {shift.attendants.map((attendant: any) => (
                                <TableCell key={`${shift.shift_number}-${attendant.employee_id}-noncash-total`} className="text-right border border-gray-300 font-semibold">
                                  {formatIDR(attendant.payment_summary.non_cash_total)}
                                </TableCell>
                              ))}
                              <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                                {formatIDR(shift.shift_summary?.total_non_cash || 0)}
                              </TableCell>
                            </React.Fragment>
                          ))}
                          <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                            {formatIDR(
                              reportData.shifts.reduce((sum: number, shift: any) =>
                                sum + (shift.shift_summary?.total_non_cash || 0), 0)
                            )}
                          </TableCell>
                        </TableRow>
                      ];
                      // 6个支付方式行，使用真实的API数据
                      bankFields.forEach((bank, i) => {
                        rows.push(
                          <TableRow key={`bank-row-${i}`}>
                            {/* 只在第一行渲染 Non-cash 合并单元格，已在上一行实现，这里不再渲染 */}
                            <TableCell className="sticky z-10 text-center font-medium border border-gray-300" style={{ backgroundColor: '#E9ECEF', color: '#495057', left: '140px' }}>{bank.name}</TableCell>
                            {reportData.shifts.map((shift) => (
                              <React.Fragment key={`shift-${shift.shift_number}-${bank.field}`}>
                                {shift.attendants.map((attendant: any) => (
                                  <TableCell key={`${shift.shift_number}-${attendant.employee_id}-${bank.field}`} className="text-right border border-gray-300">
                                    {formatIDR(attendant.payment_summary[bank.field] || 0)}
                                  </TableCell>
                                ))}
                                <TableCell className="text-right border border-gray-300 font-semibold bg-gray-50">
                                  {formatIDR((shift.shift_summary as any)?.[`total_${bank.field}`] || 0)}
                                </TableCell>
                              </React.Fragment>
                            ))}
                            <TableCell className="text-right bg-gray-100 border border-gray-300 font-semibold">
                              {formatIDR(
                                reportData.shifts.reduce((sum: number, shift: any) =>
                                  sum + ((shift.shift_summary as any)?.[`total_${bank.field}`] || 0), 0)
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      });
                      return rows;
                    })()}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>



        </CardContent>
      </Card>
    </div>
  );
} 