# Shift Management页面站点名称显示修复报告

## 📋 问题概述

**问题描述**: Shift Management相关页面中，站点名称显示为 "Unknown Station" 而不是正确的站点名称  
**修复时间**: 2025-07-20  
**修复状态**: ✅ 完全解决  
**影响范围**: Shift Management相关页面的站点名称显示  

## 🚨 问题根源分析

### 主要问题

1. **nozzle-pump-report页面**: 使用 `currentSite?.name` 而不是优先级逻辑
2. **staff-shift-report页面**: 站点名称获取逻辑不够完善，缺少 `activeSite` 支持
3. **auth-service.ts**: 默认值使用 "Unknown Station" 而不是友好的名称

### 数据流问题

```
用户登录 → activeStation/activeSite 设置 → 页面显示站点名称
```

问题出现在页面没有正确使用优先级逻辑获取站点名称。

## 🔧 修复方案

### 1. nozzle-pump-report页面修复

**文件**: `app/shift-management/nozzle-pump-report/page.tsx`

**修复内容**:
- 添加 `useAuth` 导入以获取 `activeStation` 和 `activeSite`
- 统一站点名称显示逻辑，使用与 `end-of-day-report` 相同的优先级

**修复前**:
```typescript
{currentSite?.name || 'No site selected'}
```

**修复后**:
```typescript
{(() => {
  // 使用与end-of-day-report相同的优先级逻辑
  if (activeStation) {
    return activeStation.station.site_name;
  } else if (activeSite) {
    return activeSite.siteName;
  } else if (currentSite) {
    return currentSite.name;
  } else {
    return 'No site selected';
  }
})()}
```

### 2. staff-shift-report页面修复

**文件**: `app/shift-management/staff-shift-report/page.tsx`

**修复内容**:
- 添加 `activeSite` 到 `useAuth` 解构中
- 优化站点名称获取逻辑，增加 `activeSite` 支持
- 统一默认值为 "BP Station"

**修复前**:
```typescript
const stationName = activeStation?.station?.site_name ||
                   availableSites.find(site => site.id === stationId)?.name ||
                   'Unknown Station';
```

**修复后**:
```typescript
const stationName = (() => {
  if (activeStation) {
    return activeStation.station.site_name;
  } else if (activeSite) {
    return activeSite.siteName;
  } else {
    return availableSites.find(site => site.id === stationId)?.name || 'BP Station';
  }
})();
```

**getStationName函数修复**:
```typescript
const getStationName = (stationId: string) => {
  // 优先从 activeStation 获取当前站点名称
  if (activeStation && activeStation.stationId.toString() === stationId) {
    return activeStation.station.site_name;
  }
  // 其次从 activeSite 获取当前站点名称
  if (activeSite && activeSite.siteId.toString() === stationId) {
    return activeSite.siteName;
  }
  // 再次从 currentSite 获取当前站点名称
  if (currentSite && currentSite.id.toString() === stationId) {
    return currentSite.name;
  }
  // 备用：从 availableSites 列表中查找
  const station = availableSites.find(s => s.id.toString() === stationId);
  return station?.name || "BP Station";
};
```

### 3. auth-service.ts默认值修复

**文件**: `services/auth-service.ts`

**修复内容**:
- 将默认站点名称从 "Unknown Station" 改为 "BP Station"

**修复前**:
```typescript
site_name: 'Unknown Station',
```

**修复后**:
```typescript
site_name: 'BP Station',
```

## 🎯 统一的站点名称获取优先级

所有 Shift Management 页面现在使用统一的优先级逻辑：

1. **activeStation.station.site_name** (最高优先级 - 当前活跃站点)
2. **activeSite.siteName** (次优先级 - 当前活跃站点)
3. **currentSite.name** (备用 - 当前站点上下文)
4. **availableSites查找** (备用 - 从可用站点列表查找)
5. **"BP Station"** (最后降级 - 友好的默认值)

## 🚀 修复验证

### 测试场景

1. **正常登录用户**: 显示真实的站点名称
2. **数据加载中**: 显示合理的默认值
3. **数据加载失败**: 显示 "BP Station" 而不是 "Unknown Station"

### 预期效果

- ✅ 所有 Shift Management 页面显示正确的站点名称
- ✅ 不再出现 "Unknown Station" 文本
- ✅ 与 end-of-day-report 页面保持一致的显示逻辑
- ✅ 提供友好的用户体验

## 📁 修改文件列表

1. `app/shift-management/nozzle-pump-report/page.tsx`
2. `app/shift-management/staff-shift-report/page.tsx`
3. `services/auth-service.ts`

## 🔍 相关文档

- [End of Day Report 修复文档](./END_OF_DAY_FIXES_SUMMARY.md)
- [站点数据集成文档](./station-data-integration.md)
- [BOS站点筛选器修复文档](./bos-station-filter-fixes-summary.md)
