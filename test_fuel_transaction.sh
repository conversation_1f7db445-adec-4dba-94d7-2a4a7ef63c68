#!/bin/bash

# 测试燃油交易接口
echo "Testing fuel transaction API..."

# 测试1: 使用下划线命名 (station_id)
echo "Test 1: Using snake_case (station_id)"
curl -X POST http://localhost:8080/api/v1/fuel-transactions \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_number": "TXN001",
    "station_id": 1,
    "pump_id": "PUMP01",
    "nozzle_id": "NOZZLE01",
    "fuel_type": "GASOLINE",
    "fuel_grade": "95",
    "tank": 1,
    "unit_price": 7850.0,
    "volume": 10.0,
    "amount": 78500.0,
    "total_volume": 10.0,
    "total_amount": 78500.0
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

# 测试2: 使用驼峰命名 (stationId)
echo "Test 2: Using camelCase (stationId)"
curl -X POST http://localhost:8080/api/v1/fuel-transactions \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_number": "TXN002",
    "stationId": 1,
    "pump_id": "PUMP01",
    "nozzle_id": "NOZZLE01",
    "fuel_type": "GASOLINE",
    "fuel_grade": "95",
    "tank": 1,
    "unit_price": 7850.0,
    "volume": 10.0,
    "amount": 78500.0,
    "total_volume": 10.0,
    "total_amount": 78500.0
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

# 测试3: 缺少stationId字段
echo "Test 3: Missing stationId field"
curl -X POST http://localhost:8080/api/v1/fuel-transactions \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_number": "TXN003",
    "pump_id": "PUMP01",
    "nozzle_id": "NOZZLE01",
    "fuel_type": "GASOLINE",
    "fuel_grade": "95",
    "tank": 1,
    "unit_price": 7850.0,
    "volume": 10.0,
    "amount": 78500.0,
    "total_volume": 10.0,
    "total_amount": 78500.0
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\nTest completed."
