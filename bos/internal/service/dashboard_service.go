package service

import (
	"context"
	"fmt"
	"time"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/repository"
)

// DashboardService Dashboard业务服务层
type DashboardService struct {
	repo *repository.DashboardRepository
}

// NewDashboardService 创建新的Dashboard服务
func NewDashboardService(repo *repository.DashboardRepository) *DashboardService {
	return &DashboardService{
		repo: repo,
	}
}

// DashboardSummaryResponse Dashboard汇总响应结构
type DashboardSummaryResponse struct {
	QueryDate         string                        `json:"query_date"`
	TodayRevenue      float64                       `json:"today_revenue"`
	RevenueChange     float64                       `json:"revenue_change"`
	TodayTransactions int                           `json:"today_transactions"`
	TransactionChange float64                       `json:"transaction_change"`
	TodayVolume       float64                       `json:"today_volume"`
	VolumeChange      float64                       `json:"volume_change"`
	FuelSalesMix      []repository.FuelSalesMixItem `json:"fuel_sales_mix"`
}

// GetDashboardSummary 获取Dashboard汇总数据（支持HOS全站点模式）
func (s *DashboardService) GetDashboardSummary(ctx context.Context, stationID int64, dateStr string) (*DashboardSummaryResponse, error) {
	// 解析日期参数
	date, err := s.parseDate(dateStr)
	if err != nil {
		return nil, fmt.Errorf("日期格式无效: %w", err)
	}

	// 验证日期范围，如果是未来日期会自动调整
	adjustedDate, err := s.validateDateRange(date)
	if err != nil {
		return nil, err
	}
	date = adjustedDate

	queryDate := date.Format("2006-01-02")

	// HOS系统优化：支持全站点查询
	var summary *repository.DashboardSummary
	if stationID == 0 {
		// 全站点查询模式
		fmt.Printf("执行全站点Dashboard汇总查询，日期: %s\n", queryDate)
		summary, err = s.repo.GetDashboardSummaryAllStations(ctx, date)
	} else {
		// 单站点查询模式
		fmt.Printf("执行单站点Dashboard汇总查询，站点ID: %d，日期: %s\n", stationID, queryDate)
		summary, err = s.repo.GetDashboardSummary(ctx, stationID, date)
	}

	if err != nil {
		// 数据库查询失败时，返回空数据而不是错误
		// 这样前端可以正常显示，只是数据为0
		summary = &repository.DashboardSummary{
			QueryDate:         queryDate,
			TodayRevenue:      0,
			TodayTransactions: 0,
			TodayVolume:       0,
			FuelSalesMix:      []repository.FuelSalesMixItem{},
		}
		// 记录错误但不中断流程
		fmt.Printf("获取Dashboard汇总数据失败，使用默认值: %v\n", err)
	}

	// 尝试获取前一天数据用于计算变化率，添加容错处理
	previousRevenue := 0.0
	if prevRev, err := s.repo.GetPreviousDayRevenue(ctx, stationID, date); err != nil {
		fmt.Printf("获取前一天收入数据失败，使用默认值0: %v\n", err)
	} else {
		previousRevenue = prevRev
	}

	// 计算变化率
	revenueChange := s.calculateChangeRate(summary.TodayRevenue, previousRevenue)

	// 尝试获取前一天的交易数和销量用于计算变化率，添加容错处理
	previousSummary := &repository.DashboardSummary{
		TodayTransactions: 0,
		TodayVolume:       0,
	}
	if prevSummary, err := s.repo.GetDashboardSummary(ctx, stationID, date.AddDate(0, 0, -1)); err != nil {
		fmt.Printf("获取前一天汇总数据失败，使用默认值: %v\n", err)
	} else {
		previousSummary = prevSummary
	}

	transactionChange := s.calculateChangeRate(float64(summary.TodayTransactions), float64(previousSummary.TodayTransactions))
	volumeChange := s.calculateChangeRate(summary.TodayVolume, previousSummary.TodayVolume)

	return &DashboardSummaryResponse{
		QueryDate:         summary.QueryDate,
		TodayRevenue:      summary.TodayRevenue,
		RevenueChange:     revenueChange,
		TodayTransactions: summary.TodayTransactions,
		TransactionChange: transactionChange,
		TodayVolume:       summary.TodayVolume,
		VolumeChange:      volumeChange,
		FuelSalesMix:      summary.FuelSalesMix,
	}, nil
}

// GetSalesTrend 获取销售趋势数据
func (s *DashboardService) GetSalesTrend(ctx context.Context, stationID int64, dateStr string, days int) (*repository.SalesTrendData, error) {
	// 解析日期参数
	endDate, err := s.parseDate(dateStr)
	if err != nil {
		return nil, fmt.Errorf("日期格式无效: %w", err)
	}

	// 验证日期范围，如果是未来日期会自动调整
	adjustedEndDate, err := s.validateDateRange(endDate)
	if err != nil {
		return nil, err
	}
	endDate = adjustedEndDate

	// 验证天数参数
	if days < 1 || days > 90 {
		return nil, fmt.Errorf("天数参数无效，应在1-90之间")
	}

	queryDate := endDate.Format("2006-01-02")

	// HOS系统优化：支持全站点销售趋势查询
	var trendData *repository.SalesTrendData
	if stationID == 0 {
		// 全站点查询模式
		fmt.Printf("执行全站点销售趋势查询，结束日期: %s，天数: %d\n", queryDate, days)
		trendData, err = s.repo.GetSalesTrendAllStations(ctx, endDate, days)
	} else {
		// 单站点查询模式
		fmt.Printf("执行单站点销售趋势查询，站点ID: %d，结束日期: %s，天数: %d\n", stationID, queryDate, days)
		trendData, err = s.repo.GetSalesTrend(ctx, stationID, endDate, days)
	}

	if err != nil {
		// 数据库查询失败时，返回空的趋势数据
		fmt.Printf("获取销售趋势数据失败，返回空数据: %v\n", err)

		// 生成空的趋势数据结构
		var emptyTrendData []repository.SalesTrendItem
		startDate := endDate.AddDate(0, 0, -days+1)
		for d := startDate; !d.After(endDate); d = d.AddDate(0, 0, 1) {
			emptyTrendData = append(emptyTrendData, repository.SalesTrendItem{
				Date:         d.Format("2006-01-02"),
				Revenue:      0,
				Transactions: 0,
				Volume:       0,
			})
		}

		return &repository.SalesTrendData{
			QueryDate: queryDate,
			Days:      days,
			TrendData: emptyTrendData,
		}, nil
	}

	return trendData, nil
}

// GetFuelSalesDetail 获取燃油销售详情
func (s *DashboardService) GetFuelSalesDetail(ctx context.Context, stationID int64, dateStr string) (*repository.FuelSalesDetail, error) {
	// 解析日期参数
	date, err := s.parseDate(dateStr)
	if err != nil {
		return nil, fmt.Errorf("日期格式无效: %w", err)
	}

	// 验证日期范围，如果是未来日期会自动调整
	adjustedDate, err := s.validateDateRange(date)
	if err != nil {
		return nil, err
	}
	date = adjustedDate

	queryDate := date.Format("2006-01-02")

	// HOS系统优化：支持全站点燃油销售详情查询
	var detail *repository.FuelSalesDetail
	if stationID == 0 {
		// 全站点查询模式
		fmt.Printf("执行全站点燃油销售详情查询，日期: %s\n", queryDate)
		detail, err = s.repo.GetFuelSalesDetailAllStations(ctx, date)
	} else {
		// 单站点查询模式
		fmt.Printf("执行单站点燃油销售详情查询，站点ID: %d，日期: %s\n", stationID, queryDate)
		detail, err = s.repo.GetFuelSalesDetail(ctx, stationID, date)
	}

	if err != nil {
		// 数据库查询失败时，返回空的详情数据
		fmt.Printf("获取燃油销售详情失败，返回空数据: %v\n", err)

		return &repository.FuelSalesDetail{
			QueryDate:         queryDate,
			TotalVolume:       0,
			TotalRevenue:      0,
			TotalTransactions: 0,
			FuelTypes:         []repository.FuelSalesDetailItem{},
		}, nil
	}

	return detail, nil
}

// parseDate 解析日期字符串，如果为空则返回最近有数据的日期
func (s *DashboardService) parseDate(dateStr string) (time.Time, error) {
	if dateStr == "" {
		// 返回最近有数据的日期（Asia/Jakarta时区）
		// 默认查找最近7天内有数据的日期
		return s.getLatestDataDate(7)
	}

	// 解析YYYY-MM-DD格式的日期
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("日期格式应为YYYY-MM-DD，如：2025-01-27")
	}

	// 转换为Asia/Jakarta时区
	loc, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		return time.Time{}, fmt.Errorf("加载时区失败: %w", err)
	}

	return date.In(loc), nil
}

// validateDateRange 验证日期范围，对于未来日期自动调整为当前日期
func (s *DashboardService) validateDateRange(date time.Time) (time.Time, error) {
	loc, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		return date, fmt.Errorf("加载时区失败: %w", err)
	}

	now := time.Now().In(loc)
	adjustedDate := date

	// 如果查询未来的日期，自动调整为当前日期
	if date.After(now) {
		adjustedDate = now
		fmt.Printf("检测到未来日期 %s，自动调整为当前日期 %s\n",
			date.Format("2006-01-02"), adjustedDate.Format("2006-01-02"))
	}

	// 不能查询90天之前的数据
	ninetyDaysAgo := now.AddDate(0, 0, -90)
	if adjustedDate.Before(ninetyDaysAgo) {
		return adjustedDate, fmt.Errorf("查询日期不能超过90天前")
	}

	return adjustedDate, nil
}

// calculateChangeRate 计算变化率
func (s *DashboardService) calculateChangeRate(current, previous float64) float64 {
	if previous == 0 {
		if current > 0 {
			return 100.0 // 从0增长到有值，视为100%增长
		}
		return 0.0
	}

	change := ((current - previous) / previous) * 100
	return s.roundToTwoDecimal(change)
}

// roundToTwoDecimal 四舍五入到两位小数
func (s *DashboardService) roundToTwoDecimal(value float64) float64 {
	return float64(int(value*100+0.5)) / 100
}

// ValidateStationAccess 验证用户对站点的访问权限
// 这里简化处理，实际应该根据用户权限进行验证
func (s *DashboardService) ValidateStationAccess(ctx context.Context, userID string, stationID int64) error {
	// TODO: 实现实际的权限验证逻辑
	// 1. 从context或认证服务获取用户信息
	// 2. 检查用户是否有访问该站点的权限
	// 3. 返回相应的错误信息

	// 添加基本的参数验证
	if userID == "" {
		fmt.Printf("用户ID为空，跳过权限验证\n")
		return nil
	}

	if stationID <= 0 {
		return fmt.Errorf("无效的站点ID: %d", stationID)
	}

	// 暂时简化处理，允许所有访问
	fmt.Printf("用户 %s 访问站点 %d 的权限验证通过\n", userID, stationID)
	return nil
}

// GetDefaultStationID 获取用户的默认站点ID
// 这里简化处理，实际应该从用户信息中获取
func (s *DashboardService) GetDefaultStationID(ctx context.Context, userID int64) (int64, error) {
	// TODO: 实现实际的默认站点获取逻辑
	// 1. 从context或认证服务获取用户信息
	// 2. 返回用户的默认站点ID
	// 3. 如果用户没有默认站点，返回错误

	// 暂时返回固定值
	return 1, nil
}

// getLatestDataDate 获取最近有数据的日期
func (s *DashboardService) getLatestDataDate(maxDaysBack int) (time.Time, error) {
	// 使用默认站点ID查找最近有数据的日期
	// 这里简化处理，实际应该根据用户权限获取站点ID
	stationID := int64(1)

	// 创建一个context，这里简化处理
	ctx := context.Background()

	// 直接使用repository中已有的GetLatestDataDate方法
	return s.repo.GetLatestDataDate(ctx, stationID, maxDaysBack)
}
