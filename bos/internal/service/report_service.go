package service

import (
	"context"
)

// NozzlePumpReportHeader 报表头信息
type NozzlePumpReportHeader struct {
	SiteID     int64   `json:"site_id"`
	SiteName   string  `json:"site_name"`
	ReportDate string  `json:"report_date"`
	ReportTime string  `json:"report_time"`
	ShiftID    *string `json:"shift_id,omitempty"`
	ShiftName  *string `json:"shift_name,omitempty"`
}

// NozzlePumpReading 油枪读数
type NozzlePumpReading struct {
	NozzleID           string  `json:"nozzle_id"`
	PumpID             string  `json:"pump_id"`
	FuelType           string  `json:"fuel_type"`
	FuelGrade          string  `json:"fuel_grade"`
	FuelName           string  `json:"fuel_name"`
	OpeningReading     float64 `json:"opening_reading"`
	ClosingReading     float64 `json:"closing_reading"`
	MeterDifference    float64 `json:"meter_difference"`
	SalesVolume        float64 `json:"sales_volume"`
	SalesAmount        float64 `json:"sales_amount"`
	Variance           float64 `json:"variance"`
	VariancePercentage float64 `json:"variance_percentage"`
	Status             string  `json:"status"`
	LastUpdated        string  `json:"last_updated"`
}

// NozzlePumpReportSummary 报表汇总
type NozzlePumpReportSummary struct {
	TotalNozzles     int     `json:"total_nozzles"`
	NormalCount      int     `json:"normal_count"`
	AbnormalCount    int     `json:"abnormal_count"`
	TotalVariance    float64 `json:"total_variance"`
	TotalSalesVolume float64 `json:"total_sales_volume"`
	TotalSalesAmount float64 `json:"total_sales_amount"`
}

// NozzlePumpReportData 油枪泵码报表数据
type NozzlePumpReportData struct {
	ReportHeader   NozzlePumpReportHeader  `json:"report_header"`
	NozzleReadings []NozzlePumpReading     `json:"nozzle_readings"`
	Summary        NozzlePumpReportSummary `json:"summary"`
}

// ReportService 报表服务接口
type ReportService interface {
	// GetNozzlePumpReport 获取油枪泵码报表
	// 用于检测油枪异常，比较机械读数与实际销售体积的差异
	//
	// 参数:
	//   - ctx: 上下文
	//   - siteID: 站点ID
	//   - reportDate: 报表日期，格式: YYYY-MM-DD
	//   - shiftID: 班次ID，可选，为nil时显示全天数据
	//
	// 返回:
	//   - *NozzlePumpReportData: 油枪泵码报表数据
	//   - error: 错误信息
	GetNozzlePumpReport(ctx context.Context, siteID int64, reportDate string, shiftID *string) (*NozzlePumpReportData, error)
}
