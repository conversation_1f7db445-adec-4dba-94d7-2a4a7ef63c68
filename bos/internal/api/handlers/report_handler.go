// @title 加油站管理系统 API
// @version 1.0
// @description 这是一个加油站管理系统的API服务，提供订单、燃油交易、员工管理和报表等功能。
// @contact.name API Support
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>
// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html
// @BasePath /api/v1

package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/models"
	bosService "gitlab4.weicheche.cn/indo-bp/bos/internal/service"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// ReportHandler 处理与报表相关的请求
type ReportHandler struct {
	reportService    service.ReportService
	bosReportService bosService.ReportService // 新增BOS报表服务
}

// NewReportHandler 创建新的报表处理器
func NewReportHandler(reportService service.ReportService) *ReportHandler {
	return &ReportHandler{
		reportService: reportService,
	}
}

// NewReportHandlerWithBOS 创建包含BOS报表服务的报表处理器
func NewReportHandlerWithBOS(reportService service.ReportService, bosReportService bosService.ReportService) *ReportHandler {
	return &ReportHandler{
		reportService:    reportService,
		bosReportService: bosReportService,
	}
}

// GetPaymentMethodSummaryResponse 定义支付方式汇总响应
type GetPaymentMethodSummaryResponse struct {
	Items []repository.PaymentMethodSummary `json:"items"`
}

// GetPaymentMethodSummary 处理获取支付方式汇总的请求
// @Summary 获取支付方式汇总
// @Description 获取按支付方式的销售汇总
// @Tags 报表
// @Accept json
// @Produce json
// @Param station_id query int false "站点ID"
// @Param date_from query string false "开始日期 (格式: 2006-01-02)"
// @Param date_to query string false "结束日期 (格式: 2006-01-02)"
// @Success 200 {object} GetPaymentMethodSummaryResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /reports/payment-methods [get]
func (h *ReportHandler) GetPaymentMethodSummary(c echo.Context) error {
	ctx := context.Background()

	// 构建过滤条件
	filter := repository.ReportFilter{}

	// 解析站点ID
	if c.QueryParam("station_id") != "" {
		stationID, err := strconv.ParseInt(c.QueryParam("station_id"), 10, 64)
		if err == nil && stationID > 0 {
			id := int64ToUUID(stationID)
			filter.StationID = &id
		}
	}

	// 解析日期范围 - 使用雅加达时区
	if c.QueryParam("date_from") != "" {
		dateFrom, err := time.Parse("2006-01-02", c.QueryParam("date_from"))
		if err == nil {
			// 在雅加达时区中创建日期的开始时间
			dateFrom = time.Date(dateFrom.Year(), dateFrom.Month(), dateFrom.Day(), 0, 0, 0, 0, utils.JakartaLocation)
			filter.DateFrom = &dateFrom
		} else {
			return c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Code:    "INVALID_DATE",
				Message: "无效的开始日期格式",
				Detail:  err.Error(),
			})
		}
	}

	if c.QueryParam("date_to") != "" {
		dateTo, err := time.Parse("2006-01-02", c.QueryParam("date_to"))
		if err == nil {
			// 在雅加达时区中创建日期的结束时间
			dateTo = time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), 23, 59, 59, 999999999, utils.JakartaLocation)
			filter.DateTo = &dateTo
		} else {
			return c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Code:    "INVALID_DATE",
				Message: "无效的结束日期格式",
				Detail:  err.Error(),
			})
		}
	}

	// 调用服务获取数据
	result, err := h.reportService.GetPaymentMethodSummary(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取支付方式汇总失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, GetPaymentMethodSummaryResponse{
		Items: result,
	})
}

// GetAggregatedRevenueResponse 定义聚合收入响应
type GetAggregatedRevenueResponse struct {
	Items []repository.AggregatedRevenueDTO `json:"items"`
}

// GetAggregatedRevenue 处理获取聚合收入报表的请求
// @Summary 获取聚合收入报表
// @Description 获取聚合收入报表
// @Tags 报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Param granularity query string false "聚合粒度 (day, week, month)"
// @Success 200 {object} GetAggregatedRevenueResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /reports/revenue [get]
func (h *ReportHandler) GetAggregatedRevenue(c echo.Context) error {
	ctx := context.Background()
	filter, err := parseAggregationFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, err := h.reportService.GetAggregatedRevenue(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取聚合收入报表失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, GetAggregatedRevenueResponse{
		Items: result,
	})
}

// GetAggregatedReceivableResponse 定义油品应收汇总响应
type GetAggregatedReceivableResponse struct {
	Items []repository.AggregatedReceivableDTO `json:"items"`
}

// GetAggregatedReceivable 处理获取油品应收汇总的请求
// @Summary 获取油品应收汇总
// @Description 获取油品应收汇总
// @Tags 报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Param granularity query string false "聚合粒度 (day, week, month)"
// @Success 200 {object} GetAggregatedReceivableResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /reports/receivable [get]
func (h *ReportHandler) GetAggregatedReceivable(c echo.Context) error {
	ctx := context.Background()
	filter, err := parseAggregationFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, err := h.reportService.GetAggregatedReceivable(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取油品应收汇总失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, GetAggregatedReceivableResponse{
		Items: result,
	})
}

// GetNozzleSalesSummaryResponse 定义油枪销售汇总响应
type GetNozzleSalesSummaryResponse struct {
	Items []repository.NozzleSalesSummaryDTO `json:"items"`
}

// NozzlePumpReportResponse 定义油枪泵码报表响应
type NozzlePumpReportResponse struct {
	Code    int                              `json:"code"`
	Message string                           `json:"message"`
	Data    *bosService.NozzlePumpReportData `json:"data"`
}

// GetNozzleSalesSummary 处理获取油枪销售汇总的请求
// @Summary 获取油枪销售汇总
// @Description 获取油枪销售汇总
// @Tags 报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Param granularity query string false "聚合粒度 (day, week, month)"
// @Success 200 {object} GetNozzleSalesSummaryResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /reports/nozzle-sales [get]
func (h *ReportHandler) GetNozzleSalesSummary(c echo.Context) error {
	ctx := context.Background()
	filter, err := parseAggregationFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, err := h.reportService.GetNozzleSalesSummary(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取油枪销售汇总失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, GetNozzleSalesSummaryResponse{
		Items: result,
	})
}

// GetNozzlePumpReport 处理获取油枪泵码报表的请求
// @Summary 获取油枪泵码报表
// @Description 获取指定站点和日期的油枪泵码读数报表，用于检测油枪异常
// @Tags 报表
// @Accept json
// @Produce json
// @Param site_id query int true "站点ID"
// @Param report_date query string true "报表日期 (格式: 2006-01-02)"
// @Param shift_id query int false "班次ID (可选，不提供则显示全天数据)"
// @Success 200 {object} NozzlePumpReportResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /reports/nozzle-pump-readings [get]
func (h *ReportHandler) GetNozzlePumpReport(c echo.Context) error {
	ctx := context.Background()

	// 解析请求参数
	siteIDStr := c.QueryParam("site_id")
	if siteIDStr == "" {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "MISSING_SITE_ID",
			Message: "站点ID是必填参数",
			Detail:  "site_id parameter is required",
		})
	}

	siteID, err := strconv.ParseInt(siteIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_SITE_ID",
			Message: "无效的站点ID格式",
			Detail:  err.Error(),
		})
	}

	reportDate := c.QueryParam("report_date")
	if reportDate == "" {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "MISSING_REPORT_DATE",
			Message: "报表日期是必填参数",
			Detail:  "report_date parameter is required",
		})
	}

	// 验证日期格式
	_, err = time.Parse("2006-01-02", reportDate)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_DATE_FORMAT",
			Message: "无效的日期格式，应为YYYY-MM-DD",
			Detail:  err.Error(),
		})
	}

	// 解析可选的班次ID - 支持UUID格式
	var shiftID *string
	if shiftIDStr := c.QueryParam("shift_id"); shiftIDStr != "" {
		// 验证是否为有效的UUID或数字格式
		if _, err := uuid.Parse(shiftIDStr); err == nil {
			// 是有效的UUID
			shiftID = &shiftIDStr
		} else if _, err := strconv.ParseInt(shiftIDStr, 10, 64); err == nil {
			// 是有效的数字（向后兼容）
			shiftID = &shiftIDStr
		} else {
			return c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Code:    "INVALID_SHIFT_ID",
				Message: "无效的班次ID格式，应为UUID或数字格式",
				Detail:  fmt.Sprintf("无法解析ID: %s", shiftIDStr),
			})
		}
	}

	// 调用BOS报表服务获取数据
	if h.bosReportService == nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "SERVICE_NOT_AVAILABLE",
			Message: "油枪泵码报表服务未初始化",
			Detail:  "BOS report service is not initialized",
		})
	}

	reportData, err := h.bosReportService.GetNozzlePumpReport(ctx, siteID, reportDate, shiftID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取油枪泵码报表失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, NozzlePumpReportResponse{
		Code:    200,
		Message: "success",
		Data:    reportData,
	})
}

// GetTransactionsForReportResponse 定义交易明细响应
type GetTransactionsForReportResponse struct {
	Items []repository.TransactionDTO `json:"items"`
}

// GetTransactionsForReport 处理获取交易明细的请求
// @Summary 获取交易明细
// @Description 获取交易明细数据(用于报表)
// @Tags 报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Success 200 {object} GetTransactionsForReportResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /reports/transactions [get]
func (h *ReportHandler) GetTransactionsForReport(c echo.Context) error {
	ctx := context.Background()

	startDate, err := time.Parse("2006-01-02", c.QueryParam("start_date"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_START_DATE",
			Message: "无效的开始日期格式",
			Detail:  err.Error(),
		})
	}
	// 在雅加达时区中创建开始日期
	startDate = time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, utils.JakartaLocation)

	endDate, err := time.Parse("2006-01-02", c.QueryParam("end_date"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_END_DATE",
			Message: "无效的结束日期格式",
			Detail:  err.Error(),
		})
	}

	// 在雅加达时区中创建结束日期
	endDate = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, utils.JakartaLocation)

	// 解析站点ID列表
	var siteIDs []repository.ID
	if c.QueryParam("site_ids") != "" {
		siteIDStrings := strings.Split(c.QueryParam("site_ids"), ",")
		for _, idStr := range siteIDStrings {
			id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
			if err == nil && id > 0 {
				siteIDs = append(siteIDs, repository.IDFromInt64(id))
			}
		}
	}

	filter := repository.TransactionFilter{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
		SiteIDs:   convertToIntSlice(siteIDs),
	}

	// 调用服务获取数据
	result, err := h.reportService.GetTransactionsForReport(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取交易明细失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, GetTransactionsForReportResponse{
		Items: result,
	})
}

// GetAggregatedSalesByProductCategoryResponse 定义按商品分类销售汇总响应
type GetAggregatedSalesByProductCategoryResponse struct {
	Items []repository.AggregatedSalesDTO `json:"items"`
}

// GetAggregatedSalesByProductCategory 处理获取按商品分类销售汇总的请求
// @Summary 获取按商品分类销售汇总
// @Description 按商品分类聚合销售额
// @Tags 报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Param granularity query string false "聚合粒度 (day, week, month)"
// @Success 200 {object} GetAggregatedSalesByProductCategoryResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /reports/sales-by-category [get]
func (h *ReportHandler) GetAggregatedSalesByProductCategory(c echo.Context) error {
	ctx := context.Background()
	filter, err := parseAggregationFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, err := h.reportService.GetAggregatedSalesByProductCategory(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取按商品分类销售汇总失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, GetAggregatedSalesByProductCategoryResponse{
		Items: result,
	})
}

// 辅助函数：解析聚合过滤器
func parseAggregationFilter(c echo.Context) (repository.AggregationFilter, error) {
	startDateStr := c.QueryParam("start_date")
	endDateStr := c.QueryParam("end_date")

	if startDateStr == "" || endDateStr == "" {
		return repository.AggregationFilter{},
			fmt.Errorf("开始日期和结束日期为必填参数")
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return repository.AggregationFilter{},
			fmt.Errorf("无效的开始日期格式: %v", err)
	}
	// 在雅加达时区中创建开始日期
	startDate = time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, utils.JakartaLocation)

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return repository.AggregationFilter{},
			fmt.Errorf("无效的结束日期格式: %v", err)
	}

	// 在雅加达时区中创建结束日期
	endDate = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, utils.JakartaLocation)

	// 解析站点ID列表
	var siteIDs []repository.ID
	if c.QueryParam("site_ids") != "" {
		siteIDStrings := strings.Split(c.QueryParam("site_ids"), ",")
		for _, idStr := range siteIDStrings {
			id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
			if err == nil && id > 0 {
				siteIDs = append(siteIDs, repository.IDFromInt64(id))
			}
		}
	}

	// 解析聚合粒度
	granularity := c.QueryParam("granularity")
	if granularity == "" {
		granularity = "day" // 默认按天聚合
	} else if granularity != "day" && granularity != "week" && granularity != "month" {
		return repository.AggregationFilter{},
			fmt.Errorf("无效的聚合粒度，有效值为: day, week, month")
	}

	return repository.AggregationFilter{
		StartDate:   startDate.Format("2006-01-02"),
		EndDate:     endDate.Format("2006-01-02"),
		SiteIDs:     convertToIntSlice(siteIDs),
		Granularity: granularity,
	}, nil
}

// convertToIntSlice 将repository.ID切片转换为int切片
func convertToIntSlice(ids []repository.ID) []int {
	result := make([]int, len(ids))
	for i, id := range ids {
		result[i] = int(uuidToInt64(id))
	}
	return result
}

// GetShiftEODReportResponse 定义班次日终报表响应
type GetShiftEODReportResponse struct {
	Code    int                                `json:"code"`
	Message string                             `json:"message"`
	Data    *repository.ShiftEODReportResponse `json:"data"`
}

// GetShiftEODReport 处理获取班次日终报表的请求
// @Summary 获取班次日终报表
// @Description 获取指定站点、日期的班次日终报表数据
// @Tags 报表
// @Accept json
// @Produce json
// @Param station_id query int true "站点ID"
// @Param date query string true "查询日期 (格式: 2024-01-04)"
// @Success 200 {object} GetShiftEODReportResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /reports/shift-eod [get]
func (h *ReportHandler) GetShiftEODReport(c echo.Context) error {
	ctx := context.Background()

	// 解析并验证station_id参数
	stationIDStr := c.QueryParam("station_id")
	if stationIDStr == "" {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "MISSING_STATION_ID",
			Message: "station_id parameter is required",
			Detail:  "station_id不能为空",
		})
	}

	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil || stationID <= 0 {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_STATION_ID",
			Message: "Invalid station_id parameter",
			Detail:  "station_id必须是正整数",
		})
	}

	// 解析并验证date参数
	dateStr := c.QueryParam("date")
	if dateStr == "" {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "MISSING_DATE",
			Message: "date parameter is required",
			Detail:  "date不能为空",
		})
	}

	// 验证日期格式
	_, err = time.Parse("2006-01-02", dateStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_DATE_FORMAT",
			Message: "Invalid date format",
			Detail:  "日期格式必须为YYYY-MM-DD",
		})
	}

	// 调用服务获取数据
	result, err := h.reportService.GetShiftEODReport(ctx, int64ToUUID(stationID), dateStr)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "Failed to get shift EOD report",
			Detail:  err.Error(),
		})
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, GetShiftEODReportResponse{
		Code:    200,
		Message: "Success",
		Data:    result,
	})
}
