#!/bin/bash

# 测试UUID修复的脚本
echo "🧪 测试UUID类型修复"

# 测试数据 - 使用UUID格式的fuel_transaction_id
TEST_DATA='{
  "fuel_transaction_id": "2f9d2d7e-2803-4116-bfab-00c61e84fbfe",
  "payment_method": 2,
  "station_id": 2,
  "payment_type": "CASH",
  "is_b2b": false,
  "allocated_amount": 65000.0,
  "customer_name": "ANONIM",
  "customer_phone": "**********",
  "vehicle_type": "Car",
  "license_plate": "HJKK",
  "employee_no": "admin",
  "metadata": {
    "original_amount": 65000.0,
    "received_amount": 65000.0,
    "change_amount": 0.0,
    "user_id": "00000000-0000-0000-0000-000000000001",
    "payment_type": "CASH",
    "payment_method_name": "Cash",
    "payment_note": "Payment - promotion calculation handled by order service",
    "vehicle_type": "Car",
    "vehicle_plate": "HJKK"
  }
}'

echo "📤 发送测试请求..."
echo "请求数据: $TEST_DATA"

# 发送请求到BOS API
curl -X POST \
  http://192.168.8.114:8080/api/v1/orders \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA" \
  -v

echo ""
echo "✅ 测试完成"
