#!/bin/bash

# 油枪泵码报表API测试脚本
# 使用方法: ./test-nozzle-pump-report.sh [BASE_URL] [TOKEN]

set -e

# 默认配置
BASE_URL=${1:-"http://localhost:8080"}
TOKEN=${2:-"your_token_here"}
API_ENDPOINT="$BASE_URL/api/v1/reports/nozzle-pump-readings"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "=========================================="
echo "油枪泵码报表API测试"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "API Endpoint: $API_ENDPOINT"
echo ""

# 测试函数
test_api() {
    local test_name="$1"
    local url="$2"
    local expected_status="$3"
    
    echo -n "测试: $test_name ... "
    
    response=$(curl -s -w "\n%{http_code}" -H "Authorization: Bearer $TOKEN" "$url")
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "$expected_status" ]; then
        echo -e "${GREEN}✓ PASS${NC} (HTTP $http_code)"
        if [ "$expected_status" = "200" ]; then
            # 验证响应格式
            if echo "$body" | jq -e '.code == 200 and .data.report_header and .data.nozzle_readings and .data.summary' > /dev/null 2>&1; then
                echo -e "  ${GREEN}✓ 响应格式正确${NC}"
            else
                echo -e "  ${YELLOW}⚠ 响应格式可能有问题${NC}"
            fi
        fi
    else
        echo -e "${RED}✗ FAIL${NC} (期望: $expected_status, 实际: $http_code)"
        echo "响应内容: $body"
    fi
    echo ""
}

# 1. 基础功能测试
echo "1. 基础功能测试"
echo "----------------------------------------"
test_api "正常请求 (site_id=1, 今天)" \
    "$API_ENDPOINT?site_id=1&report_date=$(date +%Y-%m-%d)" \
    "200"

test_api "带班次参数 (site_id=1, 今天, shift_id=1)" \
    "$API_ENDPOINT?site_id=1&report_date=$(date +%Y-%m-%d)&shift_id=1" \
    "200"

# 2. 参数验证测试
echo "2. 参数验证测试"
echo "----------------------------------------"
test_api "缺少site_id参数" \
    "$API_ENDPOINT?report_date=$(date +%Y-%m-%d)" \
    "400"

test_api "缺少report_date参数" \
    "$API_ENDPOINT?site_id=1" \
    "400"

test_api "无效的site_id格式" \
    "$API_ENDPOINT?site_id=invalid&report_date=$(date +%Y-%m-%d)" \
    "400"

test_api "无效的日期格式" \
    "$API_ENDPOINT?site_id=1&report_date=2024-1-4" \
    "400"

test_api "无效的shift_id格式" \
    "$API_ENDPOINT?site_id=1&report_date=$(date +%Y-%m-%d)&shift_id=invalid" \
    "400"

# 3. 边界条件测试
echo "3. 边界条件测试"
echo "----------------------------------------"
test_api "未来日期" \
    "$API_ENDPOINT?site_id=1&report_date=2025-12-31" \
    "200"

test_api "很久以前的日期" \
    "$API_ENDPOINT?site_id=1&report_date=2020-01-01" \
    "200"

test_api "不存在的站点ID" \
    "$API_ENDPOINT?site_id=99999&report_date=$(date +%Y-%m-%d)" \
    "200"

# 4. 认证测试
echo "4. 认证测试"
echo "----------------------------------------"
echo -n "测试: 无认证token ... "
response=$(curl -s -w "\n%{http_code}" "$API_ENDPOINT?site_id=1&report_date=$(date +%Y-%m-%d)")
http_code=$(echo "$response" | tail -n1)

if [ "$http_code" = "401" ] || [ "$http_code" = "403" ]; then
    echo -e "${GREEN}✓ PASS${NC} (HTTP $http_code)"
else
    echo -e "${YELLOW}⚠ 可能需要检查认证机制${NC} (HTTP $http_code)"
fi
echo ""

# 5. 性能测试
echo "5. 性能测试"
echo "----------------------------------------"
echo -n "测试: 响应时间 ... "
start_time=$(date +%s.%N)
curl -s -H "Authorization: Bearer $TOKEN" "$API_ENDPOINT?site_id=1&report_date=$(date +%Y-%m-%d)" > /dev/null
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)

if (( $(echo "$duration < 2.0" | bc -l) )); then
    echo -e "${GREEN}✓ PASS${NC} (${duration}s)"
else
    echo -e "${YELLOW}⚠ 响应较慢${NC} (${duration}s)"
fi
echo ""

# 6. 数据完整性测试
echo "6. 数据完整性测试"
echo "----------------------------------------"
echo -n "测试: 数据结构完整性 ... "
response=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_ENDPOINT?site_id=1&report_date=$(date +%Y-%m-%d)")

# 检查必要字段
required_fields=(
    ".code"
    ".message" 
    ".data.report_header.site_id"
    ".data.report_header.site_name"
    ".data.report_header.report_date"
    ".data.nozzle_readings"
    ".data.summary.total_nozzles"
)

all_fields_present=true
for field in "${required_fields[@]}"; do
    if ! echo "$response" | jq -e "$field" > /dev/null 2>&1; then
        all_fields_present=false
        break
    fi
done

if [ "$all_fields_present" = true ]; then
    echo -e "${GREEN}✓ PASS${NC}"
else
    echo -e "${RED}✗ FAIL${NC} - 缺少必要字段"
fi
echo ""

echo "=========================================="
echo "测试完成"
echo "=========================================="
echo ""
echo "注意事项:"
echo "1. 确保BOS服务正在运行"
echo "2. 确保数据库中有测试数据"
echo "3. 确保认证token有效"
echo "4. 如有测试失败，请检查服务日志"
echo ""
echo "查看服务日志命令:"
echo "  journalctl -u bos -f"
echo ""
echo "手动测试命令示例:"
echo "  curl -H \"Authorization: Bearer $TOKEN\" \"$API_ENDPOINT?site_id=1&report_date=\$(date +%Y-%m-%d)\""
