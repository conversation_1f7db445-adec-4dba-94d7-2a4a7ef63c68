package service

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// ShiftAttendantServiceImpl 班次员工服务实现
type ShiftAttendantServiceImpl struct {
	shiftRepo           repository.ShiftRepository
	shiftEODRepo        repository.ShiftEODRepository
	fuelTransactionRepo repository.FuelTransactionRepository
	orderRepo           repository.OrderRepository
	linkRepo            repository.FuelTransactionOrderLinkRepository
	shiftService        ShiftService
	employeeService     EmployeeService
	staffCardService    StaffCardService
	stationService      StationService
	paymentService      PaymentService
}

// NewShiftAttendantService 创建班次员工服务实例
func NewShiftAttendantService(
	shiftRepo repository.ShiftRepository,
	shiftEODRepo repository.ShiftEODRepository,
	fuelTransactionRepo repository.FuelTransactionRepository,
	orderRepo repository.OrderRepository,
	linkRepo repository.FuelTransactionOrderLinkRepository,
	shiftService ShiftService,
	employeeService EmployeeService,
	staffCardService StaffCardService,
	stationService StationService,
	paymentService PaymentService,
) ShiftAttendantService {
	return &ShiftAttendantServiceImpl{
		shiftRepo:           shiftRepo,
		shiftEODRepo:        shiftEODRepo,
		fuelTransactionRepo: fuelTransactionRepo,
		orderRepo:           orderRepo,
		linkRepo:            linkRepo,
		shiftService:        shiftService,
		employeeService:     employeeService,
		staffCardService:    staffCardService,
		stationService:      stationService,
		paymentService:      paymentService,
	}
}

// isTeraPayment 判断是否为tera支付方式
func (s *ShiftAttendantServiceImpl) isTeraPayment(paymentMethod string) bool {
	if paymentMethod == "" {
		return false
	}

	// 统一转换为小写进行比较
	lowerMethod := strings.ToLower(strings.TrimSpace(paymentMethod))

	// 支持的tera支付方式变体
	teraVariants := []string{
		"tera",
		"tera_payment",
		"tera_pay",
		"teraPayment",
		"terapayment",
	}

	for _, variant := range teraVariants {
		if lowerMethod == strings.ToLower(variant) {
			return true
		}
	}

	return false
}

// GetShiftAttendants 获取班次员工详细信息
func (s *ShiftAttendantServiceImpl) GetShiftAttendants(ctx context.Context, request repository.ShiftAttendantRequest) (repository.ShiftAttendantData, error) {
	// 1. 获取班次基本信息
	shift, err := s.shiftRepo.GetShiftByID(ctx, request.ShiftID)
	if err != nil {
		// 检查是否为班次不存在的错误
		if strings.Contains(err.Error(), "未找到班次") || strings.Contains(err.Error(), "not found") {
			return repository.ShiftAttendantData{}, fmt.Errorf("找不到指定班次: %s", request.ShiftID.String())
		}
		return repository.ShiftAttendantData{}, fmt.Errorf("获取班次信息失败: %w", err)
	}

	// 2. 构建班次基本信息
	shiftInfo := s.buildShiftInfo(ctx, shift)

	// 3. 根据班次状态选择数据源
	var attendants []repository.ShiftAttendantInfo

	if shift.EndTime != nil {
		// 已结束班次：优先使用汇总表数据
		attendants, _, err = s.getDataFromSummaryTables(ctx, request.ShiftID)
		if err != nil || len(attendants) == 0 {
			// 汇总表数据不完整，使用实时数据
			attendants, err = s.getRealtimeData(ctx, *shift)
			if err != nil {
				// 容错处理：如果实时数据也获取失败，返回空数据而不是错误
				fmt.Printf("获取实时数据失败，返回空数据: %v\n", err)
				attendants = []repository.ShiftAttendantInfo{}
			}
		}
	} else {
		// 进行中班次：使用实时数据
		attendants, err = s.getRealtimeData(ctx, *shift)
		if err != nil {
			// 容错处理：如果实时数据获取失败，返回空数据而不是错误
			fmt.Printf("获取实时数据失败，返回空数据: %v\n", err)
			attendants = []repository.ShiftAttendantInfo{}
		}
	}

	// 4. 应用筛选条件
	filteredAttendants := s.applyFilters(attendants, request)

	// 5. 构建班次汇总
	shiftSummary := s.buildShiftSummary(filteredAttendants)

	return repository.ShiftAttendantData{
		ShiftInfo:    shiftInfo,
		Attendants:   filteredAttendants,
		ShiftSummary: shiftSummary,
	}, nil
}

// buildShiftInfo 构建班次基本信息
func (s *ShiftAttendantServiceImpl) buildShiftInfo(ctx context.Context, shift *repository.Shift) repository.ShiftAttendantShiftInfo {
	status := "active"
	if shift.EndTime != nil {
		status = "closed"
	}

	// 通过stations接口获取站点名称
	stationName := fmt.Sprintf("Station %d", shift.StationID)
	if stationInfo, err := s.stationService.GetStationByID(ctx, shift.StationID); err == nil && stationInfo != nil {
		stationName = stationInfo.SiteName
	}

	return repository.ShiftAttendantShiftInfo{
		ID:          shift.ID,
		ShiftNumber: shift.ShiftNumber,
		StationID:   shift.StationID,
		StationName: stationName,
		StartTime:   shift.StartTime,
		EndTime:     shift.EndTime,
		Status:      status,
	}
}

// getDataFromSummaryTables 从汇总表获取数据
func (s *ShiftAttendantServiceImpl) getDataFromSummaryTables(ctx context.Context, shiftID repository.ID) ([]repository.ShiftAttendantInfo, string, error) {
	// 使用现有的汇总表仓库获取数据

	// 1. 获取支付方式详情
	paymentDetails, err := s.shiftEODRepo.GetShiftPaymentDetails(ctx, shiftID)
	if err != nil {
		return nil, "aggregated", fmt.Errorf("获取支付方式详情失败: %w", err)
	}

	// 2. 建立员工到支付方式的映射，同时识别tera支付的员工
	employeePaymentMap := make(map[repository.ID]map[string]*repository.ShiftPaymentDetail)
	employeeTeraAmountMap := make(map[repository.ID]float64)

	for _, paymentDetail := range paymentDetails {
		if paymentDetail.EmployeeID == nil {
			continue
		}

		employeeID := *paymentDetail.EmployeeID

		// 判断是否为tera支付方式
		if s.isTeraPayment(paymentDetail.PaymentMethod) {
			// 记录tera支付金额，但不参与后续统计
			employeeTeraAmountMap[employeeID] += paymentDetail.TotalAmount
			continue
		}

		// 非tera支付方式才参与统计
		if _, exists := employeePaymentMap[employeeID]; !exists {
			employeePaymentMap[employeeID] = make(map[string]*repository.ShiftPaymentDetail)
		}
		employeePaymentMap[employeeID][paymentDetail.PaymentMethod] = paymentDetail
	}

	// 3. 获取油品销售详情，但只统计非tera支付的交易
	fuelDetails, err := s.shiftEODRepo.GetShiftFuelDetails(ctx, shiftID)
	if err != nil {
		return nil, "aggregated", fmt.Errorf("获取油品销售详情失败: %w", err)
	}

	// 4. 按员工分组处理数据
	attendantMap := make(map[repository.ID]*repository.ShiftAttendantInfo)

	// 处理油品销售数据（只处理有非tera支付的员工）
	for _, fuelDetail := range fuelDetails {
		if fuelDetail.EmployeeID == nil {
			continue
		}

		employeeID := *fuelDetail.EmployeeID

		// 检查该员工是否有非tera支付方式，如果只有tera支付，则跳过该员工的燃油交易统计
		if _, hasNonTeraPayment := employeePaymentMap[employeeID]; !hasNonTeraPayment {
			continue
		}

		if _, exists := attendantMap[employeeID]; !exists {
			// 获取员工姓名
			attendantName := s.getEmployeeName(ctx, employeeID, false)

			attendantMap[employeeID] = &repository.ShiftAttendantInfo{
				AttendantInfo: repository.AttendantBasicInfo{
					AttendantName: attendantName,
					StaffCardID:   employeeID,
				},
				FuelSales: repository.ShiftAttendantFuelSales{
					ByGrade: []repository.ShiftAttendantFuelGrade{},
					Total:   repository.ShiftAttendantFuelTotal{},
				},
				PaymentSummary: repository.ShiftAttendantPayments{
					ByMethod: []repository.ShiftAttendantPaymentMethod{},
				},
				DryIncome: 0,
			}
		}

		attendant := attendantMap[employeeID]

		// 添加油品等级详情
		var fuelType string
		if fuelDetail.FuelType != nil {
			fuelType = *fuelDetail.FuelType
		}

		fuelGrade := repository.ShiftAttendantFuelGrade{
			FuelGrade:        fuelDetail.FuelGrade,
			FuelName:         s.getFuelName(fuelType, fuelDetail.FuelGrade),
			FuelType:         fuelType,
			SalesVolume:      s.formatVolume(fuelDetail.TotalVolume),
			GrossAmount:      fuelDetail.GrossAmount,
			DiscountAmount:   fuelDetail.TotalDiscountAmount,
			NetAmount:        fuelDetail.NetAmount,
			UnitPrice:        fuelDetail.AveragePrice,
			TransactionCount: fuelDetail.TransactionCount,
		}

		attendant.FuelSales.ByGrade = append(attendant.FuelSales.ByGrade, fuelGrade)

		// 累计油品销售汇总
		attendant.FuelSales.Total.TotalVolume += fuelDetail.TotalVolume
		attendant.FuelSales.Total.TotalGrossAmount += fuelDetail.GrossAmount
		attendant.FuelSales.Total.TotalDiscountAmount += fuelDetail.TotalDiscountAmount
		attendant.FuelSales.Total.TotalNetAmount += fuelDetail.NetAmount
		attendant.FuelSales.Total.TotalTransactions += fuelDetail.TransactionCount
	}

	// 5. 处理支付方式数据（只处理非tera支付）
	for employeeID, paymentMethods := range employeePaymentMap {
		attendant, exists := attendantMap[employeeID]
		if !exists {
			continue // 跳过没有油品销售数据的员工
		}

		// 计算该员工的销售支付总额（不包括tera）
		var salesPaymentAmount float64
		for _, paymentDetail := range paymentMethods {
			salesPaymentAmount += paymentDetail.TotalAmount
		}

		// 处理每种支付方式
		for _, paymentDetail := range paymentMethods {
			// 计算百分比（基于销售金额，不包括tera）
			var percentage float64
			if salesPaymentAmount > 0 {
				percentage = (paymentDetail.TotalAmount / salesPaymentAmount) * 100
			}

			// 添加支付方式详情
			paymentMethod := repository.ShiftAttendantPaymentMethod{
				PaymentMethod:     paymentDetail.PaymentMethod,
				PaymentMethodName: s.getPaymentMethodName(paymentDetail.PaymentMethod),
				TotalAmount:       paymentDetail.TotalAmount,
				TransactionCount:  paymentDetail.TransactionCount,
				Percentage:        percentage,
			}

			attendant.PaymentSummary.ByMethod = append(attendant.PaymentSummary.ByMethod, paymentMethod)

			// 按支付方式分类累计
			switch paymentDetail.PaymentMethod {
			case "cash":
				attendant.PaymentSummary.Cash += paymentDetail.TotalAmount
			case "pvc", "pvs":
				attendant.PaymentSummary.PVC += paymentDetail.TotalAmount
			case "cimb":
				attendant.PaymentSummary.CIMB += paymentDetail.TotalAmount
			case "bca":
				attendant.PaymentSummary.BCA += paymentDetail.TotalAmount
			case "mandiri":
				attendant.PaymentSummary.Mandiri += paymentDetail.TotalAmount
			case "bri":
				attendant.PaymentSummary.BRI += paymentDetail.TotalAmount
			case "bni":
				attendant.PaymentSummary.BNI += paymentDetail.TotalAmount
			case "voucher", "coupon":
				attendant.PaymentSummary.Voucher += paymentDetail.TotalAmount
			case "b2b":
				attendant.PaymentSummary.B2B += paymentDetail.TotalAmount
			default:
				// 其他支付方式归为非现金
				attendant.PaymentSummary.NonCashTotal += paymentDetail.TotalAmount
			}
		}

		// 设置tera支付金额（单独记录，不参与销售统计）
		if teraAmount, exists := employeeTeraAmountMap[employeeID]; exists {
			attendant.PaymentSummary.Tera = teraAmount
		}
	}

	// 6. 处理只有tera支付的员工（确保他们也出现在统计中）
	for employeeID, teraAmount := range employeeTeraAmountMap {
		// 如果该员工不在attendantMap中（说明只有tera支付），则创建记录
		if _, exists := attendantMap[employeeID]; !exists {
			// 获取员工姓名
			attendantName := s.getEmployeeName(ctx, employeeID, false)

			attendantMap[employeeID] = &repository.ShiftAttendantInfo{
				AttendantInfo: repository.AttendantBasicInfo{
					AttendantName: attendantName,
					StaffCardID:   employeeID,
				},
				FuelSales: repository.ShiftAttendantFuelSales{
					ByGrade: []repository.ShiftAttendantFuelGrade{},
					Total:   repository.ShiftAttendantFuelTotal{}, // 所有字段默认为0
				},
				PaymentSummary: repository.ShiftAttendantPayments{
					ByMethod: []repository.ShiftAttendantPaymentMethod{},
					Tera:     teraAmount, // 设置tera金额
				},
				DryIncome: 0,
				// TransactionCount, SalesVolumeLtr, SalesAmountIDR, GrandTotal 都默认为0
			}
		}
	}

	// 7. 计算每个员工的汇总数据
	for _, attendant := range attendantMap {
		// 设置基本统计信息
		attendant.TransactionCount = attendant.FuelSales.Total.TotalTransactions
		attendant.SalesVolumeLtr = attendant.FuelSales.Total.TotalVolume
		attendant.SalesAmountIDR = attendant.FuelSales.Total.TotalNetAmount

		// 计算非现金总额（不包括tera）
		attendant.PaymentSummary.NonCashTotal = attendant.PaymentSummary.PVC + attendant.PaymentSummary.CIMB +
			attendant.PaymentSummary.BCA + attendant.PaymentSummary.Mandiri + attendant.PaymentSummary.BRI +
			attendant.PaymentSummary.BNI + attendant.PaymentSummary.Voucher + attendant.PaymentSummary.B2B

		// 计算销售总额（不包括tera）
		salesTotal := attendant.PaymentSummary.Cash + attendant.PaymentSummary.NonCashTotal
		attendant.GrandTotal = salesTotal + attendant.DryIncome
	}

	// 8. 转换为数组
	var attendants []repository.ShiftAttendantInfo
	for _, attendant := range attendantMap {
		attendants = append(attendants, *attendant)
	}

	return attendants, "aggregated", nil
}

// getRealtimeData 获取实时数据
func (s *ShiftAttendantServiceImpl) getRealtimeData(ctx context.Context, shift repository.Shift) ([]repository.ShiftAttendantInfo, error) {
	// 1. 构建时间范围筛选条件
	endTime := time.Now()
	if shift.EndTime != nil {
		endTime = *shift.EndTime
	}

	// 2. 先查询班次时间范围内的支付数据，识别tera支付的订单
	payments, err := s.paymentService.GetPaymentsByStationAndTimeRange(ctx, shift.StationID, shift.StartTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("查询支付数据失败: %w", err)
	}

	// 3. 获取支付方式配置
	paymentMethods, err := s.paymentService.GetPaymentMethods(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取支付方式配置失败: %w", err)
	}

	// 创建支付方式映射表
	paymentMethodMap := make(map[int64]repository.PaymentMethod)
	for _, method := range paymentMethods {
		paymentMethodMap[method.ID] = method
	}

	// 4. 建立订单到支付方式的映射，识别tera支付的订单
	orderTeraPaymentMap := make(map[string]bool)
	orderToPaymentMap := make(map[string][]repository.PaymentInfo)

	for _, payment := range payments {
		orderToPaymentMap[payment.OrderID] = append(orderToPaymentMap[payment.OrderID], payment)

		// 获取支付方式信息
		var paymentMethodCode string
		if payment.PaymentMethod != nil {
			if method, exists := paymentMethodMap[*payment.PaymentMethod]; exists {
				paymentMethodCode = method.Name
			} else {
				paymentMethodCode = strings.ToLower(payment.PaymentType)
			}
		} else {
			paymentMethodCode = strings.ToLower(payment.PaymentType)
		}

		// 如果订单包含tera支付，标记该订单
		if s.isTeraPayment(paymentMethodCode) {
			orderTeraPaymentMap[payment.OrderID] = true
		}
	}

	// 5. 查询燃油交易数据
	status := repository.FuelTransactionStatusProcessed
	filter := repository.FuelTransactionFilter{
		StationID: &shift.StationID,
		DateFrom:  &shift.StartTime,
		DateTo:    &endTime,
		Status:    &status,
	}

	transactions, _, err := s.fuelTransactionRepo.List(ctx, filter, repository.Pagination{Page: 1, Limit: 1000}, repository.SortOrder{Field: "created_at", Direction: "desc"})
	if err != nil {
		return nil, fmt.Errorf("查询燃油交易失败: %w", err)
	}

	// 5.1. 批量获取所有燃油交易的折扣金额
	discountMap, err := s.calculateFuelDiscounts(ctx, transactions)
	if err != nil {
		// 记录错误但不阻断流程，使用空的折扣映射
		discountMap = make(map[repository.ID]float64)
	}

	// 6. 按员工姓名分组处理数据，排除tera支付的交易
	attendantMap := make(map[string]*repository.ShiftAttendantInfo)

	for _, transaction := range transactions {
		// 优先使用staff_card_id，如果没有则使用employee_id
		var employeeID repository.ID
		var hasEmployee bool

		if transaction.StaffCardID != nil {
			employeeID = *transaction.StaffCardID
			hasEmployee = true
		} else if transaction.EmployeeID != nil {
			employeeID = *transaction.EmployeeID
			hasEmployee = true
		}

		if !hasEmployee {
			continue
		}

		// 获取员工真实姓名
		attendantName := s.getEmployeeName(ctx, employeeID, transaction.StaffCardID != nil)
		if attendantName == "" {
			continue // 跳过无法获取姓名的员工
		}

		// 通过订单链接找到对应的订单，检查是否为tera支付
		links, err := s.linkRepo.ListByFuelTransaction(ctx, transaction.ID)
		if err != nil {
			continue
		}

		// 检查该燃油交易对应的订单是否包含tera支付
		hasTeraPayment := false
		for _, link := range links {
			// 获取订单信息
			order, err := s.orderRepo.Get(ctx, link.OrderID)
			if err != nil {
				continue
			}

			// 检查该订单是否包含tera支付
			if isTeraOrder, exists := orderTeraPaymentMap[order.OrderNumber]; exists && isTeraOrder {
				hasTeraPayment = true
				break
			}
		}

		// 如果该燃油交易对应的订单包含tera支付，则跳过该交易
		if hasTeraPayment {
			continue
		}

		// 使用员工姓名作为key进行分组
		if _, exists := attendantMap[attendantName]; !exists {
			attendantMap[attendantName] = &repository.ShiftAttendantInfo{
				AttendantInfo: repository.AttendantBasicInfo{
					AttendantName: attendantName,
					StaffCardID:   employeeID, // 保留一个ID作为参考
				},
				FuelSales: repository.ShiftAttendantFuelSales{
					ByGrade: []repository.ShiftAttendantFuelGrade{},
					Total:   repository.ShiftAttendantFuelTotal{},
				},
				PaymentSummary: repository.ShiftAttendantPayments{
					ByMethod: []repository.ShiftAttendantPaymentMethod{},
				},
				DryIncome: 0, // 默认为0
			}
		}

		// 获取该交易的折扣金额
		discountAmount := discountMap[transaction.ID]

		// 累计油品销售数据，传入真实的折扣金额
		s.accumulateFuelSales(attendantMap[attendantName], transaction, discountAmount)
	}

	// 7. 查询支付数据并累计（排除tera支付）
	err = s.accumulatePaymentDataOptimized(ctx, attendantMap, shift)
	if err != nil {
		return nil, fmt.Errorf("累计支付数据失败: %w", err)
	}

	// 8. 处理只有tera支付的员工（确保他们也出现在统计中）
	err = s.addTeraOnlyAttendantsForRealtime(ctx, attendantMap, payments, paymentMethodMap)
	if err != nil {
		return nil, fmt.Errorf("处理tera支付员工失败: %w", err)
	}

	// 9. 转换为数组并计算汇总
	attendants := make([]repository.ShiftAttendantInfo, 0, len(attendantMap))
	for _, attendant := range attendantMap {
		s.calculateAttendantTotals(attendant)
		attendants = append(attendants, *attendant)
	}

	return attendants, nil
}

// accumulateFuelSales 累计油品销售数据
func (s *ShiftAttendantServiceImpl) accumulateFuelSales(attendant *repository.ShiftAttendantInfo, transaction repository.FuelTransaction, discountAmount float64) {
	// 查找是否已有该油品等级的记录
	var gradeIndex = -1
	for i, grade := range attendant.FuelSales.ByGrade {
		if grade.FuelGrade == transaction.FuelGrade {
			gradeIndex = i
			break
		}
	}

	if gradeIndex == -1 {
		// 新增油品等级记录
		newGrade := repository.ShiftAttendantFuelGrade{
			FuelGrade:        transaction.FuelGrade,
			FuelName:         s.getFuelName(transaction.FuelType, transaction.FuelGrade),
			FuelType:         transaction.FuelType,
			SalesVolume:      s.formatVolume(transaction.Volume),  // volume保留3位小数
			GrossAmount:      transaction.Amount,                  // 修正：直接使用毛金额（transaction.Amount已经是unitPrice * volume）
			DiscountAmount:   discountAmount,                      // 从订单获取的真实折扣金额
			NetAmount:        transaction.Amount - discountAmount, // 修正：毛金额 - 折扣金额 = 净金额
			UnitPrice:        transaction.UnitPrice,
			TransactionCount: 1,
		}
		attendant.FuelSales.ByGrade = append(attendant.FuelSales.ByGrade, newGrade)
	} else {
		// 累计现有记录
		grade := &attendant.FuelSales.ByGrade[gradeIndex]
		grade.SalesVolume = s.formatVolume(grade.SalesVolume + transaction.Volume) // volume保留3位小数
		grade.GrossAmount += transaction.Amount                                    // 修正：累计毛金额
		grade.DiscountAmount += discountAmount                                     // 累计折扣金额
		grade.NetAmount += (transaction.Amount - discountAmount)                   // 修正：累计净金额（毛金额 - 折扣）
		grade.TransactionCount++

		// 重新计算平均单价（基于毛金额更合理）
		if grade.SalesVolume > 0 {
			grade.UnitPrice = grade.GrossAmount / grade.SalesVolume
		}
	}
}

// accumulatePaymentData 累计支付数据
func (s *ShiftAttendantServiceImpl) accumulatePaymentData(ctx context.Context, attendantMap map[string]*repository.ShiftAttendantInfo, shift repository.Shift) error {
	// 1. 首先获取支付方式配置
	paymentMethods, err := s.paymentService.GetPaymentMethods(ctx)
	if err != nil {
		return fmt.Errorf("获取支付方式配置失败: %w", err)
	}

	// 创建支付方式映射表
	paymentMethodMap := make(map[int64]repository.PaymentMethod)
	for _, method := range paymentMethods {
		paymentMethodMap[method.ID] = method
	}

	// 2. 构建时间范围
	endTime := time.Now()
	if shift.EndTime != nil {
		endTime = *shift.EndTime
	}

	// 3. 查询班次时间范围内的支付数据
	payments, err := s.paymentService.GetPaymentsByStationAndTimeRange(ctx, shift.StationID, shift.StartTime, endTime)
	if err != nil {
		return fmt.Errorf("查询支付数据失败: %w", err)
	}

	// 4. 创建订单到员工的映射
	orderToAttendantMap := make(map[string]string)

	// 5. 查询班次时间范围内的燃油交易，建立订单到员工的映射
	status := repository.FuelTransactionStatusProcessed
	filter := repository.FuelTransactionFilter{
		StationID: &shift.StationID,
		DateFrom:  &shift.StartTime,
		DateTo:    &endTime,
		Status:    &status,
	}

	fuelTransactions, _, err := s.fuelTransactionRepo.List(ctx, filter, repository.Pagination{Page: 1, Limit: 1000}, repository.SortOrder{Field: "created_at", Direction: "desc"})
	if err != nil {
		return fmt.Errorf("查询燃油交易失败: %w", err)
	}

	// 6. 为每个燃油交易建立订单到员工的映射
	for _, ft := range fuelTransactions {
		// 获取员工信息
		var attendantName string
		if ft.StaffCardID != nil {
			attendantName = s.getEmployeeName(ctx, *ft.StaffCardID, true)
		} else if ft.EmployeeID != nil {
			attendantName = s.getEmployeeName(ctx, *ft.EmployeeID, false)
		}

		if attendantName == "" {
			continue
		}

		// 通过订单链接找到对应的订单
		links, err := s.linkRepo.ListByFuelTransaction(ctx, ft.ID)
		if err != nil {
			continue
		}

		for _, link := range links {
			// 获取订单信息
			order, err := s.orderRepo.Get(ctx, link.OrderID)
			if err != nil {
				continue
			}

			// 建立订单号到员工的映射
			orderToAttendantMap[order.OrderNumber] = attendantName
		}
	}

	// 7. 按员工分组统计支付数据
	for _, payment := range payments {
		attendantName, exists := orderToAttendantMap[payment.OrderID]
		if !exists {
			continue // 跳过无法找到员工的支付记录
		}

		// 检查员工是否在当前统计范围内
		attendant, exists := attendantMap[attendantName]
		if !exists {
			continue // 跳过不在当前员工列表中的支付记录
		}

		// 获取支付方式信息
		var paymentMethodCode string

		if payment.PaymentMethod != nil {
			if method, exists := paymentMethodMap[*payment.PaymentMethod]; exists {
				paymentMethodCode = method.Name
			} else {
				paymentMethodCode = strings.ToLower(payment.PaymentType)
			}
		} else {
			paymentMethodCode = strings.ToLower(payment.PaymentType)
		}

		// 累计支付方式数据
		s.accumulatePaymentMethod(attendant, paymentMethodCode, payment.Amount, 1)
	}

	// 8. 计算每个员工的支付汇总
	for _, attendant := range attendantMap {
		s.calculatePaymentSummary(attendant)
	}

	return nil
}

// calculateAttendantTotals 计算员工汇总数据
func (s *ShiftAttendantServiceImpl) calculateAttendantTotals(attendant *repository.ShiftAttendantInfo) {
	// 计算油品销售汇总
	var totalVolume, totalGrossAmount, totalDiscountAmount, totalNetAmount float64
	var totalTransactions int

	for _, grade := range attendant.FuelSales.ByGrade {
		totalVolume += grade.SalesVolume
		totalGrossAmount += grade.GrossAmount
		totalDiscountAmount += grade.DiscountAmount
		totalNetAmount += grade.NetAmount
		totalTransactions += grade.TransactionCount
	}

	attendant.FuelSales.Total = repository.ShiftAttendantFuelTotal{
		TotalVolume:         totalVolume,
		TotalGrossAmount:    totalGrossAmount,
		TotalDiscountAmount: totalDiscountAmount,
		TotalNetAmount:      totalNetAmount,
		TotalTransactions:   totalTransactions,
	}

	// 设置基本统计信息
	attendant.TransactionCount = totalTransactions
	attendant.SalesVolumeLtr = totalVolume
	attendant.SalesAmountIDR = totalNetAmount

	// 计算销售总额（不包括tera支付方式）
	salesTotal := attendant.PaymentSummary.Cash + attendant.PaymentSummary.NonCashTotal
	attendant.GrandTotal = salesTotal + attendant.DryIncome
}

// applyFilters 应用筛选条件
func (s *ShiftAttendantServiceImpl) applyFilters(attendants []repository.ShiftAttendantInfo, request repository.ShiftAttendantRequest) []repository.ShiftAttendantInfo {
	if request.AttendantName == nil && request.FuelGrade == nil && request.PaymentMethod == nil {
		return attendants
	}

	filtered := make([]repository.ShiftAttendantInfo, 0)

	for _, attendant := range attendants {
		// 员工姓名筛选
		if request.AttendantName != nil {
			filterName := strings.ToLower(*request.AttendantName)
			attendantName := strings.ToLower(attendant.AttendantInfo.AttendantName)
			if !strings.Contains(attendantName, filterName) {
				continue
			}
		}

		// 油品等级筛选
		if request.FuelGrade != nil {
			hasGrade := false
			for _, grade := range attendant.FuelSales.ByGrade {
				if grade.FuelGrade == *request.FuelGrade {
					hasGrade = true
					break
				}
			}
			if !hasGrade {
				continue
			}
		}

		// 支付方式筛选
		if request.PaymentMethod != nil {
			hasPaymentMethod := false
			for _, method := range attendant.PaymentSummary.ByMethod {
				if method.PaymentMethod == *request.PaymentMethod {
					hasPaymentMethod = true
					break
				}
			}
			if !hasPaymentMethod {
				continue
			}
		}

		filtered = append(filtered, attendant)
	}

	return filtered
}

// buildShiftSummary 构建班次汇总
func (s *ShiftAttendantServiceImpl) buildShiftSummary(attendants []repository.ShiftAttendantInfo) repository.ShiftAttendantSummary {
	summary := repository.ShiftAttendantSummary{
		TotalAttendants: len(attendants),
	}

	for _, attendant := range attendants {
		summary.TotalTransactions += attendant.TransactionCount
		summary.TotalSalesVolume += attendant.SalesVolumeLtr
		summary.TotalSalesAmount += attendant.SalesAmountIDR
		summary.TotalCash += attendant.PaymentSummary.Cash
		summary.TotalNonCash += attendant.PaymentSummary.NonCashTotal
		summary.TotalPVC += attendant.PaymentSummary.PVC
		summary.TotalCIMB += attendant.PaymentSummary.CIMB
		summary.TotalBCA += attendant.PaymentSummary.BCA
		summary.TotalMandiri += attendant.PaymentSummary.Mandiri
		summary.TotalBRI += attendant.PaymentSummary.BRI
		summary.TotalBNI += attendant.PaymentSummary.BNI
		summary.TotalVoucher += attendant.PaymentSummary.Voucher
		summary.TotalB2B += attendant.PaymentSummary.B2B
		// tera支付方式不参与销售统计，只在总结中单独返回
		summary.TotalTera += attendant.PaymentSummary.Tera
		summary.GrandTotal += attendant.GrandTotal
	}

	return summary
}

// accumulatePaymentMethod 累计支付方式数据
func (s *ShiftAttendantServiceImpl) accumulatePaymentMethod(attendant *repository.ShiftAttendantInfo, paymentMethod string, amount float64, count int) {
	// 查找是否已有该支付方式的记录
	var methodIndex = -1
	for i, method := range attendant.PaymentSummary.ByMethod {
		if method.PaymentMethod == paymentMethod {
			methodIndex = i
			break
		}
	}

	if methodIndex == -1 {
		// 新增支付方式记录
		newMethod := repository.ShiftAttendantPaymentMethod{
			PaymentMethod:     paymentMethod,
			PaymentMethodName: s.getPaymentMethodName(paymentMethod),
			TotalAmount:       amount,
			TransactionCount:  count,
		}
		attendant.PaymentSummary.ByMethod = append(attendant.PaymentSummary.ByMethod, newMethod)
	} else {
		// 累计现有记录
		method := &attendant.PaymentSummary.ByMethod[methodIndex]
		method.TotalAmount += amount
		method.TransactionCount += count
	}
}

// calculatePaymentSummary 计算支付汇总
func (s *ShiftAttendantServiceImpl) calculatePaymentSummary(attendant *repository.ShiftAttendantInfo) {
	var salesAmount float64 // 用于销售统计的金额（不包括tera）

	for _, method := range attendant.PaymentSummary.ByMethod {
		salesAmount += method.TotalAmount

		// 按支付方式分类累计（tera 已经被排除，不会出现在 ByMethod 中）
		switch method.PaymentMethod {
		case "cash":
			attendant.PaymentSummary.Cash += method.TotalAmount
		case "pvc", "pvs":
			attendant.PaymentSummary.PVC += method.TotalAmount
		case "cimb":
			attendant.PaymentSummary.CIMB += method.TotalAmount
		case "bca":
			attendant.PaymentSummary.BCA += method.TotalAmount
		case "mandiri":
			attendant.PaymentSummary.Mandiri += method.TotalAmount
		case "bri":
			attendant.PaymentSummary.BRI += method.TotalAmount
		case "bni":
			attendant.PaymentSummary.BNI += method.TotalAmount
		case "voucher", "coupon":
			attendant.PaymentSummary.Voucher += method.TotalAmount
		case "b2b":
			attendant.PaymentSummary.B2B += method.TotalAmount
		default:
			// 其他支付方式归为非现金
			attendant.PaymentSummary.NonCashTotal += method.TotalAmount
		}
	}

	// 计算非现金总额（不包括tera）
	attendant.PaymentSummary.NonCashTotal = salesAmount - attendant.PaymentSummary.Cash

	// 计算百分比（基于销售金额，不包括tera）
	for idx, method := range attendant.PaymentSummary.ByMethod {
		if salesAmount > 0 {
			attendant.PaymentSummary.ByMethod[idx].Percentage = (method.TotalAmount / salesAmount) * 100
		}
	}
}

// getPaymentMethodName 获取支付方式显示名称
func (s *ShiftAttendantServiceImpl) getPaymentMethodName(paymentMethod string) string {
	switch paymentMethod {
	case "cash":
		return "现金"
	case "pvc", "pvs":
		return "PVC"
	case "cimb":
		return "CIMB"
	case "bca":
		return "BCA"
	case "mandiri":
		return "Mandiri"
	case "bri":
		return "BRI"
	case "bni":
		return "BNI"
	case "voucher", "coupon":
		return "代金券"
	case "b2b":
		return "企业支付"
	case "tera", "tera_payment":
		return "Tera"
	case "bank_card":
		return "银行卡"
	case "points":
		return "积分支付"
	default:
		return paymentMethod
	}
}

// getEmployeeName 获取员工真实姓名
func (s *ShiftAttendantServiceImpl) getEmployeeName(ctx context.Context, employeeID repository.ID, isStaffCard bool) string {
	if isStaffCard {
		// 根据staff_id获取staff_cards表的user_id，然后根据user_id获取core_service库的users表里面的full_name
		staffCardWithUser, err := s.staffCardService.GetStaffCardWithUser(ctx, employeeID)
		if err == nil && staffCardWithUser.User != nil && staffCardWithUser.User.FullName != "" {
			return staffCardWithUser.User.FullName
		}

		// 如果获取用户信息失败，尝试从metadata中获取原始员工姓名
		if staffCard, err := s.staffCardService.GetStaffCard(ctx, employeeID); err == nil && staffCard.Metadata != nil {
			if originalName, exists := staffCard.Metadata["original_employee_name"]; exists {
				if name, ok := originalName.(string); ok && name != "" {
					return name
				}
			}
		}
	} else {
		// 对于employee_id，需要在staff_cards表中查找对应的记录，然后获取user信息
		// 因为attendant name的获取只能从users表中获取，不能从employee表获取

		// 方法1：尝试直接在staff_cards表中查找是否有id匹配的记录
		staffCardWithUser, err := s.staffCardService.GetStaffCardWithUser(ctx, employeeID)
		if err == nil && staffCardWithUser.User != nil && staffCardWithUser.User.FullName != "" {
			return staffCardWithUser.User.FullName
		}

		// 方法2：查找所有员工卡，通过metadata匹配employee_id
		staffCards, _, err := s.staffCardService.ListStaffCards(ctx, repository.StaffCardFilter{}, repository.Pagination{Limit: 1000}, repository.SortOrder{})
		if err == nil {
			// 在内存中查找匹配的员工卡
			for _, card := range staffCards {
				if card.Metadata != nil {
					// TODO: 修复员工ID的UUID转换逻辑
					// 暂时跳过这个逻辑，需要重新设计
					_ = card // 避免未使用变量警告
					continue
				}
			}
		}
	}

	// 方法3：如果通过metadata没有找到，尝试通过员工编号匹配
	// 获取员工信息（如果存在的话）
	employee, err := s.employeeService.GetEmployee(ctx, employeeID)
	if err == nil {
		// 重新获取员工卡列表
		staffCards2, _, err := s.staffCardService.ListStaffCards(ctx, repository.StaffCardFilter{}, repository.Pagination{Limit: 1000}, repository.SortOrder{})
		if err == nil {
			// 通过员工编号查找对应的员工卡
			for _, card := range staffCards2 {
				if card.Metadata != nil {
					if originalEmployeeNo, exists := card.Metadata["original_employee_no"]; exists {
						if employeeNo, ok := originalEmployeeNo.(string); ok && employeeNo == employee.EmployeeNo {
							// 找到对应的员工卡，获取用户信息
							staffCardWithUser, err := s.staffCardService.GetStaffCardWithUser(ctx, card.ID)
							if err == nil && staffCardWithUser.User != nil && staffCardWithUser.User.FullName != "" {
								return staffCardWithUser.User.FullName
							}
							break
						}
					}
				}
			}
		}
	}

	// 如果无法获取真实姓名，返回默认格式
	return fmt.Sprintf("Employee %s", employeeID.String())
}

// getFuelName 获取油品显示名称
func (s *ShiftAttendantServiceImpl) getFuelName(fuelType, fuelGrade string) string {
	// fuel_name不要加工，与fuel_grade一致
	return fuelGrade
}

// formatVolume 格式化volume，保留3位小数
func (s *ShiftAttendantServiceImpl) formatVolume(volume float64) float64 {
	return math.Round(volume*1000) / 1000
}

// calculateTotalPaymentAmount 计算指定员工的总支付金额（包括 tera）
func (s *ShiftAttendantServiceImpl) calculateTotalPaymentAmount(paymentDetails []*repository.ShiftPaymentDetail, employeeID repository.ID) float64 {
	var total float64
	for _, detail := range paymentDetails {
		if detail.EmployeeID != nil && *detail.EmployeeID == employeeID {
			total += detail.TotalAmount
		}
	}
	return total
}

// calculateSalesPaymentAmount 计算指定员工的销售支付金额（不包括 tera）
func (s *ShiftAttendantServiceImpl) calculateSalesPaymentAmount(paymentDetails []*repository.ShiftPaymentDetail, employeeID repository.ID) float64 {
	var total float64
	for _, detail := range paymentDetails {
		if detail.EmployeeID != nil && *detail.EmployeeID == employeeID {
			// 排除 tera 支付方式
			if !s.isTeraPayment(detail.PaymentMethod) {
				total += detail.TotalAmount
			}
		}
	}
	return total
}

// accumulatePaymentDataOptimized 优化的支付数据累计方法
func (s *ShiftAttendantServiceImpl) accumulatePaymentDataOptimized(ctx context.Context, attendantMap map[string]*repository.ShiftAttendantInfo, shift repository.Shift) error {
	// 1. 首先获取支付方式配置
	paymentMethods, err := s.paymentService.GetPaymentMethods(ctx)
	if err != nil {
		return fmt.Errorf("获取支付方式配置失败: %w", err)
	}

	// 创建支付方式映射表
	paymentMethodMap := make(map[int64]repository.PaymentMethod)
	for _, method := range paymentMethods {
		paymentMethodMap[method.ID] = method
	}

	// 2. 构建时间范围
	endTime := time.Now()
	if shift.EndTime != nil {
		endTime = *shift.EndTime
	}

	// 3. 查询班次时间范围内的支付数据
	payments, err := s.paymentService.GetPaymentsByStationAndTimeRange(ctx, shift.StationID, shift.StartTime, endTime)
	if err != nil {
		return fmt.Errorf("查询支付数据失败: %w", err)
	}

	// 4. 查询燃油交易数据以建立订单到员工的映射
	status := repository.FuelTransactionStatusProcessed
	filter := repository.FuelTransactionFilter{
		StationID: &shift.StationID,
		DateFrom:  &shift.StartTime,
		DateTo:    &endTime,
		Status:    &status,
	}

	fuelTransactions, _, err := s.fuelTransactionRepo.List(ctx, filter, repository.Pagination{Page: 1, Limit: 1000}, repository.SortOrder{Field: "created_at", Direction: "desc"})
	if err != nil {
		return fmt.Errorf("查询燃油交易失败: %w", err)
	}

	// 5. 建立订单号到员工的映射
	orderToAttendantMap := make(map[string]string)
	for _, ft := range fuelTransactions {
		// 获取员工信息
		var attendantName string
		if ft.StaffCardID != nil {
			attendantName = s.getEmployeeName(ctx, *ft.StaffCardID, true)
		} else if ft.EmployeeID != nil {
			attendantName = s.getEmployeeName(ctx, *ft.EmployeeID, false)
		}

		if attendantName == "" {
			continue
		}

		// 通过订单链接找到对应的订单
		links, err := s.linkRepo.ListByFuelTransaction(ctx, ft.ID)
		if err != nil {
			continue
		}

		for _, link := range links {
			// 获取订单信息
			order, err := s.orderRepo.Get(ctx, link.OrderID)
			if err != nil {
				continue
			}

			// 建立订单号到员工的映射
			orderToAttendantMap[order.OrderNumber] = attendantName
		}
	}

	// 6. 按员工分组统计支付数据，并分离 tera 支付
	for _, payment := range payments {
		attendantName, exists := orderToAttendantMap[payment.OrderID]
		if !exists {
			continue // 跳过无法找到员工的支付记录
		}

		// 检查员工是否在当前统计范围内
		attendant, exists := attendantMap[attendantName]
		if !exists {
			continue // 跳过不在当前员工列表中的支付记录
		}

		// 获取支付方式信息
		var paymentMethodCode string

		if payment.PaymentMethod != nil {
			if method, exists := paymentMethodMap[*payment.PaymentMethod]; exists {
				paymentMethodCode = method.Name
			} else {
				paymentMethodCode = strings.ToLower(payment.PaymentType)
			}
		} else {
			paymentMethodCode = strings.ToLower(payment.PaymentType)
		}

		// 判断是否为 tera 支付方式
		if s.isTeraPayment(paymentMethodCode) {
			// 只记录到 Tera 字段中，不添加到 ByMethod 数组
			attendant.PaymentSummary.Tera += payment.Amount
			continue
		}

		// 累计支付方式数据
		s.accumulatePaymentMethodOptimized(attendant, paymentMethodCode, payment.Amount, 1)
	}

	// 7. 计算每个员工的支付汇总（优化版本）
	for _, attendant := range attendantMap {
		s.calculatePaymentSummaryOptimized(attendant)
	}

	return nil
}

// accumulatePaymentMethodOptimized 优化的支付方式数据累计
func (s *ShiftAttendantServiceImpl) accumulatePaymentMethodOptimized(attendant *repository.ShiftAttendantInfo, paymentMethod string, amount float64, count int) {
	// 查找是否已有该支付方式的记录
	var methodIndex = -1
	for i, method := range attendant.PaymentSummary.ByMethod {
		if method.PaymentMethod == paymentMethod {
			methodIndex = i
			break
		}
	}

	if methodIndex == -1 {
		// 新增支付方式记录
		newMethod := repository.ShiftAttendantPaymentMethod{
			PaymentMethod:     paymentMethod,
			PaymentMethodName: s.getPaymentMethodName(paymentMethod),
			TotalAmount:       amount,
			TransactionCount:  count,
		}
		attendant.PaymentSummary.ByMethod = append(attendant.PaymentSummary.ByMethod, newMethod)
	} else {
		// 累计现有记录
		method := &attendant.PaymentSummary.ByMethod[methodIndex]
		method.TotalAmount += amount
		method.TransactionCount += count
	}
}

// calculatePaymentSummaryOptimized 优化的支付汇总计算
func (s *ShiftAttendantServiceImpl) calculatePaymentSummaryOptimized(attendant *repository.ShiftAttendantInfo) {
	var salesAmount float64 // 用于销售统计的金额（不包括tera）

	for _, method := range attendant.PaymentSummary.ByMethod {
		salesAmount += method.TotalAmount

		// 按支付方式分类累计（tera 已经被排除，不会出现在 ByMethod 中）
		switch method.PaymentMethod {
		case "cash":
			attendant.PaymentSummary.Cash += method.TotalAmount
		case "pvc", "pvs":
			attendant.PaymentSummary.PVC += method.TotalAmount
		case "cimb":
			attendant.PaymentSummary.CIMB += method.TotalAmount
		case "bca":
			attendant.PaymentSummary.BCA += method.TotalAmount
		case "mandiri":
			attendant.PaymentSummary.Mandiri += method.TotalAmount
		case "bri":
			attendant.PaymentSummary.BRI += method.TotalAmount
		case "bni":
			attendant.PaymentSummary.BNI += method.TotalAmount
		case "voucher", "coupon":
			attendant.PaymentSummary.Voucher += method.TotalAmount
		case "b2b":
			attendant.PaymentSummary.B2B += method.TotalAmount
		default:
			// 其他支付方式归为非现金
			attendant.PaymentSummary.NonCashTotal += method.TotalAmount
		}
	}

	// 计算非现金总额（不包括tera）
	attendant.PaymentSummary.NonCashTotal = salesAmount - attendant.PaymentSummary.Cash

	// 计算百分比（基于销售金额，不包括tera）
	for idx, method := range attendant.PaymentSummary.ByMethod {
		if salesAmount > 0 {
			attendant.PaymentSummary.ByMethod[idx].Percentage = (method.TotalAmount / salesAmount) * 100
		}
	}
}

// addTeraOnlyAttendants 添加只有tera支付的员工到统计中
func (s *ShiftAttendantServiceImpl) addTeraOnlyAttendants(ctx context.Context, attendantMap map[string]*repository.ShiftAttendantInfo, payments []repository.PaymentInfo, paymentMethodMap map[int64]repository.PaymentMethod, orderToAttendantMap map[string]string) error {
	// 统计每个员工的tera支付金额
	attendantTeraMap := make(map[string]float64)

	for _, payment := range payments {
		attendantName, exists := orderToAttendantMap[payment.OrderID]
		if !exists {
			continue
		}

		// 获取支付方式信息
		var paymentMethodCode string
		if payment.PaymentMethod != nil {
			if method, exists := paymentMethodMap[*payment.PaymentMethod]; exists {
				paymentMethodCode = method.Name
			} else {
				paymentMethodCode = strings.ToLower(payment.PaymentType)
			}
		} else {
			paymentMethodCode = strings.ToLower(payment.PaymentType)
		}

		// 如果是tera支付，累计金额
		if s.isTeraPayment(paymentMethodCode) {
			attendantTeraMap[attendantName] += payment.Amount
		}
	}

	// 为只有tera支付的员工创建记录
	for attendantName, teraAmount := range attendantTeraMap {
		// 如果该员工不在attendantMap中（说明只有tera支付），则创建记录
		if _, exists := attendantMap[attendantName]; !exists {
			attendantMap[attendantName] = &repository.ShiftAttendantInfo{
				AttendantInfo: repository.AttendantBasicInfo{
					AttendantName: attendantName,
					StaffCardID:   repository.ID(uuid.Nil), // 只有姓名，没有具体ID
				},
				FuelSales: repository.ShiftAttendantFuelSales{
					ByGrade: []repository.ShiftAttendantFuelGrade{},
					Total:   repository.ShiftAttendantFuelTotal{}, // 所有字段默认为0
				},
				PaymentSummary: repository.ShiftAttendantPayments{
					ByMethod: []repository.ShiftAttendantPaymentMethod{},
					Tera:     teraAmount, // 设置tera金额
				},
				DryIncome: 0,
				// TransactionCount, SalesVolumeLtr, SalesAmountIDR, GrandTotal 都默认为0
			}
		}
	}

	return nil
}

// calculateFuelDiscounts 批量计算燃油交易的折扣金额
func (s *ShiftAttendantServiceImpl) calculateFuelDiscounts(ctx context.Context, transactions []repository.FuelTransaction) (map[repository.ID]float64, error) {
	if len(transactions) == 0 {
		return make(map[repository.ID]float64), nil
	}

	// 提取所有交易ID
	transactionIDs := make([]repository.ID, len(transactions))
	for i, tx := range transactions {
		transactionIDs[i] = tx.ID
	}

	// 批量查询折扣金额
	discountMap, err := s.orderRepo.GetFuelTransactionDiscountAmounts(ctx, transactionIDs)
	if err != nil {
		// 记录错误但不阻断流程，返回空的折扣映射
		// 这样即使折扣查询失败，主流程仍可继续
		return make(map[repository.ID]float64), nil
	}

	return discountMap, nil
}

// getDiscountAmountForTransaction 获取单个燃油交易的折扣金额 (为保持向后兼容性)
func (s *ShiftAttendantServiceImpl) getDiscountAmountForTransaction(ctx context.Context, transactionID repository.ID) float64 {
	discountAmount, err := s.orderRepo.GetFuelTransactionDiscountAmount(ctx, transactionID)
	if err != nil {
		// 记录日志但不阻断流程
		return 0
	}
	return discountAmount
}

// addTeraOnlyAttendantsForRealtime 为实时数据添加只有tera支付的员工到统计中
func (s *ShiftAttendantServiceImpl) addTeraOnlyAttendantsForRealtime(ctx context.Context, attendantMap map[string]*repository.ShiftAttendantInfo, payments []repository.PaymentInfo, paymentMethodMap map[int64]repository.PaymentMethod) error {
	// 统计每个员工的tera支付金额
	attendantTeraMap := make(map[string]float64)

	for _, payment := range payments {
		// 获取支付方式信息
		var paymentMethodCode string
		if payment.PaymentMethod != nil {
			if method, exists := paymentMethodMap[*payment.PaymentMethod]; exists {
				paymentMethodCode = method.Name
			} else {
				paymentMethodCode = strings.ToLower(payment.PaymentType)
			}
		} else {
			paymentMethodCode = strings.ToLower(payment.PaymentType)
		}

		// 如果是tera支付，需要找到对应的员工
		if s.isTeraPayment(paymentMethodCode) {
			// 通过订单ID查找对应的燃油交易，从而找到员工
			attendantName, err := s.getAttendantNameByOrderID(ctx, payment.OrderID)
			if err != nil || attendantName == "" {
				continue
			}
			attendantTeraMap[attendantName] += payment.Amount
		}
	}

	// 为只有tera支付的员工创建记录
	for attendantName, teraAmount := range attendantTeraMap {
		// 如果该员工不在attendantMap中（说明只有tera支付），则创建记录
		if _, exists := attendantMap[attendantName]; !exists {
			attendantMap[attendantName] = &repository.ShiftAttendantInfo{
				AttendantInfo: repository.AttendantBasicInfo{
					AttendantName: attendantName,
					StaffCardID:   repository.ID(uuid.Nil), // 只有姓名，没有具体ID
				},
				FuelSales: repository.ShiftAttendantFuelSales{
					ByGrade: []repository.ShiftAttendantFuelGrade{},
					Total:   repository.ShiftAttendantFuelTotal{}, // 所有字段默认为0
				},
				PaymentSummary: repository.ShiftAttendantPayments{
					ByMethod: []repository.ShiftAttendantPaymentMethod{},
					Tera:     teraAmount, // 设置tera金额
				},
				DryIncome: 0,
				// TransactionCount, SalesVolumeLtr, SalesAmountIDR, GrandTotal 都默认为0
			}
		}
	}

	return nil
}

// getAttendantNameByOrderID 通过订单号获取员工姓名
func (s *ShiftAttendantServiceImpl) getAttendantNameByOrderID(ctx context.Context, orderNumber string) (string, error) {
	// 先通过订单号获取订单
	order, err := s.orderRepo.GetByOrderNumber(ctx, orderNumber)
	if err != nil {
		return "", err
	}

	// 通过订单ID查找关联的燃油交易
	links, err := s.linkRepo.ListByOrder(ctx, order.ID)
	if err != nil {
		return "", err
	}

	for _, link := range links {
		// 获取燃油交易信息
		transaction, err := s.fuelTransactionRepo.Get(ctx, link.FuelTransactionID)
		if err != nil {
			continue
		}

		// 获取员工ID
		var employeeID repository.ID
		var hasEmployee bool

		if transaction.StaffCardID != nil {
			employeeID = *transaction.StaffCardID
			hasEmployee = true
		} else if transaction.EmployeeID != nil {
			employeeID = *transaction.EmployeeID
			hasEmployee = true
		}

		if hasEmployee {
			// 获取员工姓名
			attendantName := s.getEmployeeName(ctx, employeeID, transaction.StaffCardID != nil)
			if attendantName != "" {
				return attendantName, nil
			}
		}
	}

	return "", nil
}
