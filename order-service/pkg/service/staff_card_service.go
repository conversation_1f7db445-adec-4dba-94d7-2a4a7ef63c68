package service

import (
	"context"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// StaffCardService 员工卡服务接口
type StaffCardService interface {
	// CreateStaffCard 创建员工卡
	CreateStaffCard(ctx context.Context, staffCard repository.StaffCard) (repository.StaffCard, error)

	// UpdateStaffCard 更新员工卡
	UpdateStaffCard(ctx context.Context, staffCard repository.StaffCard) (repository.StaffCard, error)

	// GetStaffCard 获取员工卡详情
	GetStaffCard(ctx context.Context, id repository.ID) (repository.StaffCard, error)

	// GetStaffCardByNumber 根据卡号获取员工卡
	GetStaffCardByNumber(ctx context.Context, cardNumber string) (repository.StaffCard, error)

	// ListStaffCards 获取员工卡列表
	ListStaffCards(ctx context.Context, filter repository.StaffCardFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.StaffCard, int, error)

	// DeleteStaffCard 删除员工卡
	DeleteStaffCard(ctx context.Context, id repository.ID) error

	// ActivateCard 激活员工卡
	ActivateCard(ctx context.Context, cardNumber string) error

	// SuspendCard 暂停员工卡
	SuspendCard(ctx context.Context, cardNumber string, reason string) error

	// ValidateCardForTransaction 验证员工卡是否可用于交易
	ValidateCardForTransaction(ctx context.Context, cardNumber string, stationID int64) (repository.StaffCardValidationResult, error)

	// GetUserStaffCards 获取用户的所有员工卡
	GetUserStaffCards(ctx context.Context, userID string) ([]repository.StaffCard, error)

	// CreateStaffCardForUser 为用户创建员工卡（自动生成卡号）
	CreateStaffCardForUser(ctx context.Context, userID string, cardType repository.StaffCardType, stationID *int64) (repository.StaffCard, error)

	// CreateStaffCardForUserWithCardNumber 为用户创建员工卡（使用指定卡号）
	CreateStaffCardForUserWithCardNumber(ctx context.Context, userID string, cardNumber string, cardType repository.StaffCardType, stationID *int64) (repository.StaffCard, error)

	// ExtendCardValidity 延长员工卡有效期
	ExtendCardValidity(ctx context.Context, cardNumber string, newValidUntil *time.Time) error

	// GetActiveCardsByStation 获取指定站点的活跃员工卡
	GetActiveCardsByStation(ctx context.Context, stationID int64) ([]repository.StaffCard, error)

	// GetStaffCardWithUser 获取员工卡信息并包含用户信息
	GetStaffCardWithUser(ctx context.Context, id repository.ID) (repository.StaffCard, error)

	// ListStaffCardsWithUser 获取员工卡列表并包含用户信息
	ListStaffCardsWithUser(ctx context.Context, filter repository.StaffCardFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.StaffCard, int, error)

	// BulkCreateStaffCards 批量创建员工卡
	BulkCreateStaffCards(ctx context.Context, userIDs []string, cardType repository.StaffCardType, stationID *int64) ([]repository.StaffCard, error)
}
