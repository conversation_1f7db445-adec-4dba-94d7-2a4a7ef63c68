package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// StaffCardServiceImpl 实现StaffCardService接口
type StaffCardServiceImpl struct {
	staffCardRepo repository.StaffCardRepository
}

// NewStaffCardService 创建一个新的StaffCardService实例
func NewStaffCardService(staffCardRepo repository.StaffCardRepository) StaffCardService {
	return &StaffCardServiceImpl{
		staffCardRepo: staffCardRepo,
	}
}

// CreateStaffCard 创建员工卡
func (s *StaffCardServiceImpl) CreateStaffCard(ctx context.Context, staffCard repository.StaffCard) (repository.StaffCard, error) {
	// 验证员工卡信息
	if err := s.validateStaffCard(staffCard); err != nil {
		return repository.StaffCard{}, err
	}

	// 如果没有提供卡号，自动生成
	if staffCard.CardNumber == "" {
		cardNumber, err := s.generateCardNumber()
		if err != nil {
			return repository.StaffCard{}, fmt.Errorf("生成卡号失败: %w", err)
		}
		staffCard.CardNumber = cardNumber
	}

	// 设置默认值
	if staffCard.Status == "" {
		staffCard.Status = repository.StaffCardStatusActive
	}

	if staffCard.CardType == "" {
		staffCard.CardType = repository.StaffCardTypeEmployee
	}

	if staffCard.ValidFrom.IsZero() {
		staffCard.ValidFrom = time.Now()
	}

	// 验证用户是否存在
	_, err := s.staffCardRepo.GetUserInfo(ctx, staffCard.UserID)
	if err != nil {
		return repository.StaffCard{}, fmt.Errorf("用户不存在: %w", err)
	}

	// 创建员工卡
	return s.staffCardRepo.Create(ctx, staffCard)
}

// UpdateStaffCard 更新员工卡
func (s *StaffCardServiceImpl) UpdateStaffCard(ctx context.Context, staffCard repository.StaffCard) (repository.StaffCard, error) {
	// 检查员工卡ID是否有效
	if uuid.UUID(staffCard.ID) == uuid.Nil {
		return repository.StaffCard{}, errors.New("无效的员工卡ID")
	}

	// 获取现有员工卡信息
	existingCard, err := s.staffCardRepo.Get(ctx, staffCard.ID)
	if err != nil {
		return repository.StaffCard{}, fmt.Errorf("获取员工卡信息失败: %w", err)
	}

	// 合并更新信息（某些字段不允许修改）
	staffCard.CardNumber = existingCard.CardNumber // 卡号不允许修改
	staffCard.UserID = existingCard.UserID         // 用户ID不允许修改
	staffCard.CreatedAt = existingCard.CreatedAt   // 创建时间不允许修改

	// 如果没有提供某些字段，使用现有值
	if staffCard.CardType == "" {
		staffCard.CardType = existingCard.CardType
	}
	if staffCard.Status == "" {
		staffCard.Status = existingCard.Status
	}
	if staffCard.ValidFrom.IsZero() {
		staffCard.ValidFrom = existingCard.ValidFrom
	}

	// 验证更新后的信息
	if err := s.validateStaffCard(staffCard); err != nil {
		return repository.StaffCard{}, err
	}

	// 更新员工卡
	return s.staffCardRepo.Update(ctx, staffCard)
}

// GetStaffCard 获取员工卡详情
func (s *StaffCardServiceImpl) GetStaffCard(ctx context.Context, id repository.ID) (repository.StaffCard, error) {
	if uuid.UUID(id) == uuid.Nil {
		return repository.StaffCard{}, errors.New("无效的员工卡ID")
	}

	return s.staffCardRepo.Get(ctx, id)
}

// GetStaffCardByNumber 根据卡号获取员工卡
func (s *StaffCardServiceImpl) GetStaffCardByNumber(ctx context.Context, cardNumber string) (repository.StaffCard, error) {
	fmt.Printf("StaffCardService.GetStaffCardByNumber called with cardNumber: %s\n", cardNumber)

	if cardNumber == "" {
		fmt.Println("Card number is empty in service layer")
		return repository.StaffCard{}, errors.New("员工卡号不能为空")
	}

	fmt.Printf("Calling repository.GetByCardNumber with cardNumber: %s\n", cardNumber)
	result, err := s.staffCardRepo.GetByCardNumber(ctx, cardNumber)
	if err != nil {
		fmt.Printf("Repository returned error: %v\n", err)
		return repository.StaffCard{}, err
	}

	fmt.Printf("Repository returned staff card: %+v\n", result)
	return result, nil
}

// ListStaffCards 获取员工卡列表
func (s *StaffCardServiceImpl) ListStaffCards(ctx context.Context, filter repository.StaffCardFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.StaffCard, int, error) {
	// 验证分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.Limit <= 0 || pagination.Limit > 100 {
		pagination.Limit = 20 // 默认限制
	}

	return s.staffCardRepo.List(ctx, filter, pagination, sort)
}

// DeleteStaffCard 删除员工卡
func (s *StaffCardServiceImpl) DeleteStaffCard(ctx context.Context, id repository.ID) error {
	if uuid.UUID(id) == uuid.Nil {
		return errors.New("无效的员工卡ID")
	}

	// 检查员工卡是否存在
	_, err := s.staffCardRepo.Get(ctx, id)
	if err != nil {
		return fmt.Errorf("员工卡不存在: %w", err)
	}

	return s.staffCardRepo.Delete(ctx, id)
}

// ActivateCard 激活员工卡
func (s *StaffCardServiceImpl) ActivateCard(ctx context.Context, cardNumber string) error {
	if cardNumber == "" {
		return errors.New("员工卡号不能为空")
	}

	// 获取员工卡
	card, err := s.staffCardRepo.GetByCardNumber(ctx, cardNumber)
	if err != nil {
		return fmt.Errorf("员工卡不存在: %w", err)
	}

	// 检查当前状态
	if card.Status == repository.StaffCardStatusActive {
		return errors.New("员工卡已经是激活状态")
	}

	// 更新状态
	return s.staffCardRepo.UpdateStatus(ctx, card.ID, repository.StaffCardStatusActive, "手动激活")
}

// SuspendCard 暂停员工卡
func (s *StaffCardServiceImpl) SuspendCard(ctx context.Context, cardNumber string, reason string) error {
	if cardNumber == "" {
		return errors.New("员工卡号不能为空")
	}

	if reason == "" {
		reason = "手动暂停"
	}

	// 获取员工卡
	card, err := s.staffCardRepo.GetByCardNumber(ctx, cardNumber)
	if err != nil {
		return fmt.Errorf("员工卡不存在: %w", err)
	}

	// 检查当前状态
	if card.Status == repository.StaffCardStatusSuspended {
		return errors.New("员工卡已经是暂停状态")
	}

	// 更新状态
	return s.staffCardRepo.UpdateStatus(ctx, card.ID, repository.StaffCardStatusSuspended, reason)
}

// ValidateCardForTransaction 验证员工卡是否可用于交易
func (s *StaffCardServiceImpl) ValidateCardForTransaction(ctx context.Context, cardNumber string, stationID int64) (repository.StaffCardValidationResult, error) {
	if cardNumber == "" {
		return repository.StaffCardValidationResult{
			IsValid: false,
			Message: "员工卡号不能为空",
		}, errors.New("员工卡号不能为空")
	}

	if stationID <= 0 {
		return repository.StaffCardValidationResult{
			IsValid: false,
			Message: "无效的站点ID",
		}, errors.New("无效的站点ID")
	}

	// 使用Repository层的验证方法
	result, err := s.staffCardRepo.ValidateCard(ctx, cardNumber, &stationID)
	if err != nil {
		return repository.StaffCardValidationResult{
			IsValid: false,
			Message: "验证过程发生错误",
		}, err
	}

	return result, nil
}

// GetUserStaffCards 获取用户的所有员工卡
func (s *StaffCardServiceImpl) GetUserStaffCards(ctx context.Context, userID string) ([]repository.StaffCard, error) {
	if userID == "" {
		return nil, errors.New("用户ID不能为空")
	}

	return s.staffCardRepo.GetByUserID(ctx, userID)
}

// CreateStaffCardForUser 为用户创建员工卡（自动生成卡号）
func (s *StaffCardServiceImpl) CreateStaffCardForUser(ctx context.Context, userID string, cardType repository.StaffCardType, stationID *int64) (repository.StaffCard, error) {
	if userID == "" {
		return repository.StaffCard{}, errors.New("用户ID不能为空")
	}

	// 验证用户是否存在
	_, err := s.staffCardRepo.GetUserInfo(ctx, userID)
	if err != nil {
		return repository.StaffCard{}, fmt.Errorf("用户不存在: %w", err)
	}

	// 生成卡号
	cardNumber, err := s.generateCardNumber()
	if err != nil {
		return repository.StaffCard{}, fmt.Errorf("生成卡号失败: %w", err)
	}

	// 创建员工卡
	staffCard := repository.StaffCard{
		CardNumber:  cardNumber,
		UserID:      userID,
		StationID:   stationID,
		CardType:    cardType,
		Status:      repository.StaffCardStatusActive,
		ValidFrom:   time.Now(),
		Permissions: make(map[string]interface{}),
		Metadata:    make(map[string]interface{}),
	}

	return s.staffCardRepo.Create(ctx, staffCard)
}

// CreateStaffCardForUserWithCardNumber 为用户创建员工卡（使用指定卡号）
func (s *StaffCardServiceImpl) CreateStaffCardForUserWithCardNumber(ctx context.Context, userID string, cardNumber string, cardType repository.StaffCardType, stationID *int64) (repository.StaffCard, error) {
	if userID == "" {
		return repository.StaffCard{}, errors.New("用户ID不能为空")
	}

	if cardNumber == "" {
		return repository.StaffCard{}, errors.New("员工卡号不能为空")
	}

	// 验证用户是否存在
	_, err := s.staffCardRepo.GetUserInfo(ctx, userID)
	if err != nil {
		return repository.StaffCard{}, fmt.Errorf("用户不存在: %w", err)
	}

	// 创建员工卡
	staffCard := repository.StaffCard{
		CardNumber:  cardNumber,
		UserID:      userID,
		StationID:   stationID,
		CardType:    cardType,
		Status:      repository.StaffCardStatusActive,
		ValidFrom:   time.Now(),
		Permissions: make(map[string]interface{}),
		Metadata:    make(map[string]interface{}),
	}

	return s.staffCardRepo.Create(ctx, staffCard)
}

// ExtendCardValidity 延长员工卡有效期
func (s *StaffCardServiceImpl) ExtendCardValidity(ctx context.Context, cardNumber string, newValidUntil *time.Time) error {
	if cardNumber == "" {
		return errors.New("员工卡号不能为空")
	}

	// 获取员工卡
	card, err := s.staffCardRepo.GetByCardNumber(ctx, cardNumber)
	if err != nil {
		return fmt.Errorf("员工卡不存在: %w", err)
	}

	// 如果提供了新的有效期，验证时间是否合理
	if newValidUntil != nil {
		if newValidUntil.Before(time.Now()) {
			return errors.New("新的有效期不能早于当前时间")
		}
		if newValidUntil.Before(card.ValidFrom) {
			return errors.New("新的有效期不能早于生效时间")
		}
	}

	return s.staffCardRepo.ExtendValidity(ctx, card.ID, newValidUntil)
}

// GetActiveCardsByStation 获取指定站点的活跃员工卡
func (s *StaffCardServiceImpl) GetActiveCardsByStation(ctx context.Context, stationID int64) ([]repository.StaffCard, error) {
	if stationID <= 0 {
		return nil, errors.New("无效的站点ID")
	}

	return s.staffCardRepo.GetActiveCardsByStation(ctx, stationID)
}

// GetStaffCardWithUser 获取员工卡信息并包含用户信息
func (s *StaffCardServiceImpl) GetStaffCardWithUser(ctx context.Context, id repository.ID) (repository.StaffCard, error) {
	if uuid.UUID(id) == uuid.Nil {
		return repository.StaffCard{}, errors.New("无效的员工卡ID")
	}

	return s.staffCardRepo.GetWithUserInfo(ctx, id)
}

// ListStaffCardsWithUser 获取员工卡列表并包含用户信息
func (s *StaffCardServiceImpl) ListStaffCardsWithUser(ctx context.Context, filter repository.StaffCardFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.StaffCard, int, error) {
	// 验证分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.Limit <= 0 || pagination.Limit > 100 {
		pagination.Limit = 20 // 默认限制
	}

	return s.staffCardRepo.ListWithUserInfo(ctx, filter, pagination, sort)
}

// BulkCreateStaffCards 批量创建员工卡
func (s *StaffCardServiceImpl) BulkCreateStaffCards(ctx context.Context, userIDs []string, cardType repository.StaffCardType, stationID *int64) ([]repository.StaffCard, error) {
	if len(userIDs) == 0 {
		return nil, errors.New("用户ID列表不能为空")
	}

	if len(userIDs) > 50 {
		return nil, errors.New("批量创建数量不能超过50个")
	}

	var createdCards []repository.StaffCard
	var failedUserIDs []string

	for _, userID := range userIDs {
		card, err := s.CreateStaffCardForUser(ctx, userID, cardType, stationID)
		if err != nil {
			// 记录失败的用户ID，但继续处理其他用户
			failedUserIDs = append(failedUserIDs, userID)
			continue
		}
		createdCards = append(createdCards, card)
	}

	// 如果有失败的情况，返回部分成功的结果和错误信息
	if len(failedUserIDs) > 0 {
		return createdCards, fmt.Errorf("部分用户创建失败，失败的用户ID: %v", failedUserIDs)
	}

	return createdCards, nil
}

// 私有辅助方法

// validateStaffCard 验证员工卡信息
func (s *StaffCardServiceImpl) validateStaffCard(staffCard repository.StaffCard) error {
	if staffCard.UserID == "" {
		return errors.New("用户ID不能为空")
	}

	if staffCard.CardType == "" {
		return errors.New("员工卡类型不能为空")
	}

	// 验证卡类型
	validCardTypes := map[repository.StaffCardType]bool{
		repository.StaffCardTypeEmployee: true,
		repository.StaffCardTypeManager:  true,
		repository.StaffCardTypeAdmin:    true,
	}
	if !validCardTypes[staffCard.CardType] {
		return errors.New("无效的员工卡类型")
	}

	if staffCard.Status == "" {
		return errors.New("员工卡状态不能为空")
	}

	// 验证状态
	validStatuses := map[repository.StaffCardStatus]bool{
		repository.StaffCardStatusActive:    true,
		repository.StaffCardStatusInactive:  true,
		repository.StaffCardStatusSuspended: true,
		repository.StaffCardStatusExpired:   true,
	}
	if !validStatuses[staffCard.Status] {
		return errors.New("无效的员工卡状态")
	}

	// 验证有效期
	if staffCard.ValidUntil != nil && staffCard.ValidUntil.Before(staffCard.ValidFrom) {
		return errors.New("有效期结束时间不能早于开始时间")
	}

	return nil
}

// generateCardNumber 生成员工卡号
func (s *StaffCardServiceImpl) generateCardNumber() (string, error) {
	// 这里可以实现更复杂的卡号生成逻辑
	// 目前使用简单的时间戳+随机数方式
	now := time.Now()
	return fmt.Sprintf("SC%d%03d", now.Unix(), now.Nanosecond()%1000), nil
}
