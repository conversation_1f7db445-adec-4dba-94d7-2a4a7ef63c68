package postgres

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// StaffCardRepositoryImpl 实现了StaffCardRepository接口
type StaffCardRepositoryImpl struct {
	multiDB *database.MultiDatabase
}

// NewStaffCardRepository 创建一个新的StaffCardRepository实例
func NewStaffCardRepository(multiDB *database.MultiDatabase) repository.StaffCardRepository {
	return &StaffCardRepositoryImpl{
		multiDB: multiDB,
	}
}

// Create 创建员工卡
func (r *StaffCardRepositoryImpl) Create(ctx context.Context, staffCard repository.StaffCard) (repository.StaffCard, error) {
	var newStaffCard repository.StaffCard

	err := r.multiDB.GetOrderDB().RunInTransaction(ctx, func(tx pgx.Tx) error {
		// 设置创建时间和更新时间
		now := time.Now()
		staffCard.CreatedAt = now
		staffCard.UpdatedAt = now

		// 序列化权限和元数据
		permissionsJSON, err := json.Marshal(staffCard.Permissions)
		if err != nil {
			return fmt.Errorf("序列化权限失败: %w", err)
		}

		metadataJSON, err := json.Marshal(staffCard.Metadata)
		if err != nil {
			return fmt.Errorf("序列化元数据失败: %w", err)
		}

		// 检查卡号是否已存在
		var exists bool
		err = tx.QueryRow(ctx,
			"SELECT EXISTS(SELECT 1 FROM staff_cards WHERE card_number = $1 AND deleted_at IS NULL)",
			staffCard.CardNumber).Scan(&exists)
		if err != nil {
			return fmt.Errorf("检查卡号失败: %w", err)
		}
		if exists {
			return fmt.Errorf("员工卡号 %s 已存在", staffCard.CardNumber)
		}

		// 插入员工卡
		query := `
			INSERT INTO staff_cards (
				card_number, user_id, station_id, card_type, status,
				valid_from, valid_until, permissions, metadata,
				created_at, updated_at
			) VALUES (
				$1, $2, $3, $4, $5,
				$6, $7, $8, $9,
				$10, $11
			) RETURNING
				id, card_number, user_id, station_id, card_type, status,
				valid_from, valid_until, permissions, metadata,
				created_at, updated_at, deleted_at
		`

		var permissionsJSONDB, metadataJSONDB []byte
		err = tx.QueryRow(ctx, query,
			staffCard.CardNumber, staffCard.UserID, staffCard.StationID, staffCard.CardType, staffCard.Status,
			staffCard.ValidFrom, staffCard.ValidUntil, permissionsJSON, metadataJSON,
			staffCard.CreatedAt, staffCard.UpdatedAt,
		).Scan(
			&newStaffCard.ID, &newStaffCard.CardNumber, &newStaffCard.UserID,
			&newStaffCard.StationID, &newStaffCard.CardType, &newStaffCard.Status,
			&newStaffCard.ValidFrom, &newStaffCard.ValidUntil,
			&permissionsJSONDB, &metadataJSONDB,
			&newStaffCard.CreatedAt, &newStaffCard.UpdatedAt, &newStaffCard.DeletedAt,
		)

		if err != nil {
			return fmt.Errorf("创建员工卡失败: %w", err)
		}

		// 反序列化权限和元数据
		if len(permissionsJSONDB) > 0 {
			if err := json.Unmarshal(permissionsJSONDB, &newStaffCard.Permissions); err != nil {
				return fmt.Errorf("解析权限失败: %w", err)
			}
		}

		if len(metadataJSONDB) > 0 {
			if err := json.Unmarshal(metadataJSONDB, &newStaffCard.Metadata); err != nil {
				return fmt.Errorf("解析元数据失败: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		return repository.StaffCard{}, err
	}

	return newStaffCard, nil
}

// Update 更新员工卡
func (r *StaffCardRepositoryImpl) Update(ctx context.Context, staffCard repository.StaffCard) (repository.StaffCard, error) {
	var updatedStaffCard repository.StaffCard

	err := r.multiDB.GetOrderDB().RunInTransaction(ctx, func(tx pgx.Tx) error {
		// 更新时间
		staffCard.UpdatedAt = time.Now()

		// 序列化权限和元数据
		permissionsJSON, err := json.Marshal(staffCard.Permissions)
		if err != nil {
			return fmt.Errorf("序列化权限失败: %w", err)
		}

		metadataJSON, err := json.Marshal(staffCard.Metadata)
		if err != nil {
			return fmt.Errorf("序列化元数据失败: %w", err)
		}

		// 更新员工卡
		query := `
			UPDATE staff_cards
			SET
				card_number = $1,
				user_id = $2,
				station_id = $3,
				card_type = $4,
				status = $5,
				valid_from = $6,
				valid_until = $7,
				permissions = $8,
				metadata = $9,
				updated_at = $10
			WHERE id = $11 AND deleted_at IS NULL
			RETURNING
				id, card_number, user_id, station_id, card_type, status,
				valid_from, valid_until, permissions, metadata,
				created_at, updated_at, deleted_at
		`

		var permissionsJSONDB, metadataJSONDB []byte
		err = tx.QueryRow(ctx, query,
			staffCard.CardNumber, staffCard.UserID, staffCard.StationID,
			staffCard.CardType, staffCard.Status,
			staffCard.ValidFrom, staffCard.ValidUntil,
			permissionsJSON, metadataJSON, staffCard.UpdatedAt,
			staffCard.ID,
		).Scan(
			&updatedStaffCard.ID, &updatedStaffCard.CardNumber, &updatedStaffCard.UserID,
			&updatedStaffCard.StationID, &updatedStaffCard.CardType, &updatedStaffCard.Status,
			&updatedStaffCard.ValidFrom, &updatedStaffCard.ValidUntil,
			&permissionsJSONDB, &metadataJSONDB,
			&updatedStaffCard.CreatedAt, &updatedStaffCard.UpdatedAt, &updatedStaffCard.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return fmt.Errorf("员工卡不存在或已删除")
			}
			return fmt.Errorf("更新员工卡失败: %w", err)
		}

		// 反序列化权限和元数据
		if len(permissionsJSONDB) > 0 {
			if err := json.Unmarshal(permissionsJSONDB, &updatedStaffCard.Permissions); err != nil {
				return fmt.Errorf("解析权限失败: %w", err)
			}
		}

		if len(metadataJSONDB) > 0 {
			if err := json.Unmarshal(metadataJSONDB, &updatedStaffCard.Metadata); err != nil {
				return fmt.Errorf("解析元数据失败: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		return repository.StaffCard{}, err
	}

	return updatedStaffCard, nil
}

// Get 根据ID获取员工卡
func (r *StaffCardRepositoryImpl) Get(ctx context.Context, id repository.ID) (repository.StaffCard, error) {
	var staffCard repository.StaffCard

	query := `
		SELECT
			id, card_number, user_id, station_id, card_type, status,
			valid_from, valid_until, permissions, metadata,
			created_at, updated_at, deleted_at
		FROM staff_cards
		WHERE id = $1 AND deleted_at IS NULL
	`

	var permissionsJSON, metadataJSON []byte
	err := r.multiDB.GetOrderDB().GetPool().QueryRow(ctx, query, id).Scan(
		&staffCard.ID, &staffCard.CardNumber, &staffCard.UserID,
		&staffCard.StationID, &staffCard.CardType, &staffCard.Status,
		&staffCard.ValidFrom, &staffCard.ValidUntil,
		&permissionsJSON, &metadataJSON,
		&staffCard.CreatedAt, &staffCard.UpdatedAt, &staffCard.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return repository.StaffCard{}, fmt.Errorf("员工卡不存在或已删除")
		}
		return repository.StaffCard{}, fmt.Errorf("获取员工卡失败: %w", err)
	}

	// 反序列化权限和元数据
	if len(permissionsJSON) > 0 {
		if err := json.Unmarshal(permissionsJSON, &staffCard.Permissions); err != nil {
			return repository.StaffCard{}, fmt.Errorf("解析权限失败: %w", err)
		}
	}

	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &staffCard.Metadata); err != nil {
			return repository.StaffCard{}, fmt.Errorf("解析元数据失败: %w", err)
		}
	}

	return staffCard, nil
}

// GetByCardNumber 根据卡号获取员工卡
func (r *StaffCardRepositoryImpl) GetByCardNumber(ctx context.Context, cardNumber string) (repository.StaffCard, error) {
	var staffCard repository.StaffCard

	query := `
		SELECT
			id, card_number, user_id, station_id, card_type, status,
			valid_from, valid_until, permissions, metadata,
			created_at, updated_at, deleted_at
		FROM staff_cards
		WHERE card_number = $1 AND deleted_at IS NULL
	`

	var permissionsJSON, metadataJSON []byte
	err := r.multiDB.GetOrderDB().GetPool().QueryRow(ctx, query, cardNumber).Scan(
		&staffCard.ID, &staffCard.CardNumber, &staffCard.UserID,
		&staffCard.StationID, &staffCard.CardType, &staffCard.Status,
		&staffCard.ValidFrom, &staffCard.ValidUntil,
		&permissionsJSON, &metadataJSON,
		&staffCard.CreatedAt, &staffCard.UpdatedAt, &staffCard.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return repository.StaffCard{}, fmt.Errorf("员工卡不存在或已删除")
		}
		return repository.StaffCard{}, fmt.Errorf("获取员工卡失败: %w", err)
	}

	// 反序列化权限和元数据
	if len(permissionsJSON) > 0 {
		if err := json.Unmarshal(permissionsJSON, &staffCard.Permissions); err != nil {
			return repository.StaffCard{}, fmt.Errorf("解析权限失败: %w", err)
		}
	}

	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &staffCard.Metadata); err != nil {
			return repository.StaffCard{}, fmt.Errorf("解析元数据失败: %w", err)
		}
	}

	return staffCard, nil
}

// GetByUserID 根据用户ID获取员工卡列表
func (r *StaffCardRepositoryImpl) GetByUserID(ctx context.Context, userID string) ([]repository.StaffCard, error) {
	query := `
		SELECT
			id, card_number, user_id, station_id, card_type, status,
			valid_from, valid_until, permissions, metadata,
			created_at, updated_at, deleted_at
		FROM staff_cards
		WHERE user_id = $1 AND deleted_at IS NULL
		ORDER BY created_at DESC
	`

	rows, err := r.multiDB.GetOrderDB().GetPool().Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("查询员工卡列表失败: %w", err)
	}
	defer rows.Close()

	var staffCards []repository.StaffCard
	for rows.Next() {
		var sc repository.StaffCard
		var permissionsJSON, metadataJSON []byte

		err := rows.Scan(
			&sc.ID, &sc.CardNumber, &sc.UserID,
			&sc.StationID, &sc.CardType, &sc.Status,
			&sc.ValidFrom, &sc.ValidUntil,
			&permissionsJSON, &metadataJSON,
			&sc.CreatedAt, &sc.UpdatedAt, &sc.DeletedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("解析员工卡数据失败: %w", err)
		}

		// 反序列化权限和元数据
		if len(permissionsJSON) > 0 {
			if err := json.Unmarshal(permissionsJSON, &sc.Permissions); err != nil {
				return nil, fmt.Errorf("解析权限失败: %w", err)
			}
		}

		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &sc.Metadata); err != nil {
				return nil, fmt.Errorf("解析元数据失败: %w", err)
			}
		}

		staffCards = append(staffCards, sc)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("处理员工卡列表结果失败: %w", err)
	}

	return staffCards, nil
}

// List 获取员工卡列表
func (r *StaffCardRepositoryImpl) List(ctx context.Context, filter repository.StaffCardFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.StaffCard, int, error) {
	// 构建查询
	baseQuery := `
		FROM staff_cards
		WHERE deleted_at IS NULL
	`
	countQuery := "SELECT COUNT(*) " + baseQuery

	// 添加过滤条件
	filterQuery, args := buildStaffCardFilterQuery(filter)
	if filterQuery != "" {
		baseQuery += " AND " + filterQuery
		countQuery += " AND " + filterQuery
	}

	// 获取总数
	var total int
	err := r.multiDB.GetOrderDB().GetPool().QueryRow(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("获取员工卡总数失败: %w", err)
	}

	// 构建主查询
	selectQuery := `SELECT 
		id, card_number, user_id, station_id, card_type, status,
		valid_from, valid_until, permissions, metadata,
		created_at, updated_at, deleted_at 
	` + baseQuery

	// 添加排序
	if sort.Field != "" {
		direction := "ASC"
		if strings.ToUpper(sort.Direction) == "DESC" {
			direction = "DESC"
		}

		// 安全检查: 防止SQL注入
		validFields := map[string]bool{
			"id": true, "card_number": true, "user_id": true, "card_type": true,
			"status": true, "valid_from": true, "valid_until": true, "created_at": true, "updated_at": true,
		}
		if !validFields[sort.Field] {
			sort.Field = "id" // 默认排序字段
		}

		selectQuery += fmt.Sprintf(" ORDER BY %s %s", sort.Field, direction)
	} else {
		selectQuery += " ORDER BY created_at DESC"
	}

	// 添加分页
	if pagination.Limit > 0 {
		selectQuery += fmt.Sprintf(" LIMIT %d", pagination.Limit)
		if pagination.Page > 1 {
			offset := (pagination.Page - 1) * pagination.Limit
			selectQuery += fmt.Sprintf(" OFFSET %d", offset)
		}
	}

	// 执行查询
	rows, err := r.multiDB.GetOrderDB().GetPool().Query(ctx, selectQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询员工卡列表失败: %w", err)
	}
	defer rows.Close()

	staffCards := []repository.StaffCard{}
	for rows.Next() {
		var sc repository.StaffCard
		var permissionsJSON, metadataJSON []byte

		err := rows.Scan(
			&sc.ID, &sc.CardNumber, &sc.UserID,
			&sc.StationID, &sc.CardType, &sc.Status,
			&sc.ValidFrom, &sc.ValidUntil,
			&permissionsJSON, &metadataJSON,
			&sc.CreatedAt, &sc.UpdatedAt, &sc.DeletedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("解析员工卡数据失败: %w", err)
		}

		// 反序列化权限和元数据
		if len(permissionsJSON) > 0 {
			if err := json.Unmarshal(permissionsJSON, &sc.Permissions); err != nil {
				return nil, 0, fmt.Errorf("解析权限失败: %w", err)
			}
		}

		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &sc.Metadata); err != nil {
				return nil, 0, fmt.Errorf("解析元数据失败: %w", err)
			}
		}

		staffCards = append(staffCards, sc)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("处理员工卡列表结果失败: %w", err)
	}

	return staffCards, total, nil
}

// Delete 删除员工卡（软删除）
func (r *StaffCardRepositoryImpl) Delete(ctx context.Context, id repository.ID) error {
	now := time.Now()
	query := `
		UPDATE staff_cards
		SET deleted_at = $1, updated_at = $2
		WHERE id = $3 AND deleted_at IS NULL
	`

	result, err := r.multiDB.GetOrderDB().GetPool().Exec(ctx, query, now, now, id)
	if err != nil {
		return fmt.Errorf("删除员工卡失败: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("员工卡不存在或已被删除")
	}

	return nil
}

// ValidateCard 验证员工卡是否有效
func (r *StaffCardRepositoryImpl) ValidateCard(ctx context.Context, cardNumber string, stationID *int64) (repository.StaffCardValidationResult, error) {
	// 使用数据库函数进行验证
	query := `
		SELECT card_id, is_valid, status, user_id, validation_message
		FROM validate_staff_card($1, $2)
	`

	var result repository.StaffCardValidationResult
	var cardID *repository.ID
	var userIDStr string

	err := r.multiDB.GetOrderDB().GetPool().QueryRow(ctx, query, cardNumber, stationID).Scan(
		&cardID, &result.IsValid, &result.Status, &userIDStr, &result.Message,
	)

	if err != nil {
		return repository.StaffCardValidationResult{
			IsValid: false,
			Message: "验证过程发生错误",
		}, fmt.Errorf("验证员工卡失败: %w", err)
	}

	result.CardID = cardID
	result.UserID = userIDStr

	// 如果验证成功，获取完整的员工卡信息
	if result.IsValid && cardID != nil {
		staffCard, err := r.Get(ctx, *cardID)
		if err == nil {
			result.StaffCard = &staffCard
		}
	}

	return result, nil
}

// GetUserInfo 获取员工卡关联的用户信息（跨库查询）
func (r *StaffCardRepositoryImpl) GetUserInfo(ctx context.Context, userID string) (repository.AuthUser, error) {
	var user repository.AuthUser

	query := `
		SELECT 
			id, username, email, phone, full_name, department, status,
			created_at, updated_at
		FROM users
		WHERE id = $1 AND deleted_at IS NULL
	`

	err := r.multiDB.GetAuthDB().GetPool().QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Username, &user.Email, &user.Phone,
		&user.FullName, &user.Department, &user.Status,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return repository.AuthUser{}, fmt.Errorf("用户不存在或已删除")
		}
		return repository.AuthUser{}, fmt.Errorf("获取用户信息失败: %w", err)
	}

	return user, nil
}

// GetWithUserInfo 获取员工卡信息并包含用户信息
func (r *StaffCardRepositoryImpl) GetWithUserInfo(ctx context.Context, id repository.ID) (repository.StaffCard, error) {
	staffCard, err := r.Get(ctx, id)
	if err != nil {
		return repository.StaffCard{}, err
	}

	// 获取用户信息
	user, err := r.GetUserInfo(ctx, staffCard.UserID)
	if err != nil {
		// 如果用户信息获取失败，只记录错误但不影响员工卡信息返回
		// 实际项目中可能需要根据业务需求决定是否要求用户信息必须存在
		return staffCard, nil
	}

	staffCard.User = &user
	return staffCard, nil
}

// ListWithUserInfo 获取员工卡列表并包含用户信息
func (r *StaffCardRepositoryImpl) ListWithUserInfo(ctx context.Context, filter repository.StaffCardFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.StaffCard, int, error) {
	staffCards, total, err := r.List(ctx, filter, pagination, sort)
	if err != nil {
		return nil, 0, err
	}

	// 批量获取用户信息
	userIDs := make([]string, len(staffCards))
	for i, sc := range staffCards {
		userIDs[i] = sc.UserID
	}

	users, err := r.batchGetUserInfo(ctx, userIDs)
	if err != nil {
		// 如果批量获取失败，返回不带用户信息的员工卡列表
		return staffCards, total, nil
	}

	// 关联用户信息
	userMap := make(map[string]repository.AuthUser)
	for _, user := range users {
		userMap[user.ID] = user
	}

	for i := range staffCards {
		if user, exists := userMap[staffCards[i].UserID]; exists {
			staffCards[i].User = &user
		}
	}

	return staffCards, total, nil
}

// UpdateStatus 更新员工卡状态
func (r *StaffCardRepositoryImpl) UpdateStatus(ctx context.Context, id repository.ID, status repository.StaffCardStatus, reason string) error {
	now := time.Now()

	// 构建元数据更新，记录状态变更历史
	metadataUpdate := map[string]interface{}{
		"status_change": map[string]interface{}{
			"new_status": status,
			"reason":     reason,
			"changed_at": now,
			"changed_by": "system", // 可以根据上下文获取操作用户
		},
	}

	metadataJSON, err := json.Marshal(metadataUpdate)
	if err != nil {
		return fmt.Errorf("序列化元数据失败: %w", err)
	}

	query := `
		UPDATE staff_cards
		SET 
			status = $1,
			metadata = metadata || $2,
			updated_at = $3
		WHERE id = $4 AND deleted_at IS NULL
	`

	result, err := r.multiDB.GetOrderDB().GetPool().Exec(ctx, query, status, metadataJSON, now, id)
	if err != nil {
		return fmt.Errorf("更新员工卡状态失败: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("员工卡不存在或已被删除")
	}

	return nil
}

// ExtendValidity 延长员工卡有效期
func (r *StaffCardRepositoryImpl) ExtendValidity(ctx context.Context, id repository.ID, newValidUntil *time.Time) error {
	now := time.Now()

	query := `
		UPDATE staff_cards
		SET 
			valid_until = $1,
			updated_at = $2
		WHERE id = $3 AND deleted_at IS NULL
	`

	result, err := r.multiDB.GetOrderDB().GetPool().Exec(ctx, query, newValidUntil, now, id)
	if err != nil {
		return fmt.Errorf("延长员工卡有效期失败: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("员工卡不存在或已被删除")
	}

	return nil
}

// GetActiveCardsByStation 获取指定站点的活跃员工卡
func (r *StaffCardRepositoryImpl) GetActiveCardsByStation(ctx context.Context, stationID int64) ([]repository.StaffCard, error) {
	query := `
		SELECT
			id, card_number, user_id, station_id, card_type, status,
			valid_from, valid_until, permissions, metadata,
			created_at, updated_at, deleted_at
		FROM staff_cards
		WHERE deleted_at IS NULL
			AND status = 'active'
			AND (station_id IS NULL OR station_id = $1)
			AND (valid_until IS NULL OR valid_until > NOW())
			AND valid_from <= NOW()
		ORDER BY created_at DESC
	`

	rows, err := r.multiDB.GetOrderDB().GetPool().Query(ctx, query, stationID)
	if err != nil {
		return nil, fmt.Errorf("查询活跃员工卡失败: %w", err)
	}
	defer rows.Close()

	var staffCards []repository.StaffCard
	for rows.Next() {
		var sc repository.StaffCard
		var permissionsJSON, metadataJSON []byte

		err := rows.Scan(
			&sc.ID, &sc.CardNumber, &sc.UserID,
			&sc.StationID, &sc.CardType, &sc.Status,
			&sc.ValidFrom, &sc.ValidUntil,
			&permissionsJSON, &metadataJSON,
			&sc.CreatedAt, &sc.UpdatedAt, &sc.DeletedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("解析员工卡数据失败: %w", err)
		}

		// 反序列化权限和元数据
		if len(permissionsJSON) > 0 {
			if err := json.Unmarshal(permissionsJSON, &sc.Permissions); err != nil {
				return nil, fmt.Errorf("解析权限失败: %w", err)
			}
		}

		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &sc.Metadata); err != nil {
				return nil, fmt.Errorf("解析元数据失败: %w", err)
			}
		}

		staffCards = append(staffCards, sc)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("处理活跃员工卡列表结果失败: %w", err)
	}

	return staffCards, nil
}

// 辅助方法
// buildStaffCardFilterQuery 构建员工卡过滤查询语句
func buildStaffCardFilterQuery(filter repository.StaffCardFilter) (string, []interface{}) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	if filter.UserID != nil {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, *filter.UserID)
		argIndex++
	}

	if filter.StationID != nil {
		conditions = append(conditions, fmt.Sprintf("station_id = $%d", argIndex))
		args = append(args, *filter.StationID)
		argIndex++
	}

	if filter.CardType != nil {
		conditions = append(conditions, fmt.Sprintf("card_type = $%d", argIndex))
		args = append(args, *filter.CardType)
		argIndex++
	}

	if filter.Status != nil {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *filter.Status)
		argIndex++
	}

	if filter.CardNumber != nil {
		conditions = append(conditions, fmt.Sprintf("card_number ILIKE $%d", argIndex))
		args = append(args, "%"+*filter.CardNumber+"%")
		argIndex++
	}

	if filter.DateFrom != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *filter.DateFrom)
		argIndex++
	}

	if filter.DateTo != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *filter.DateTo)
		argIndex++
	}

	if filter.ValidOnly != nil && *filter.ValidOnly {
		conditions = append(conditions, "status = 'active'")
		conditions = append(conditions, "valid_from <= NOW()")
		conditions = append(conditions, "(valid_until IS NULL OR valid_until > NOW())")
	}

	return strings.Join(conditions, " AND "), args
}

// batchGetUserInfo 批量获取用户信息
func (r *StaffCardRepositoryImpl) batchGetUserInfo(ctx context.Context, userIDs []string) ([]repository.AuthUser, error) {
	if len(userIDs) == 0 {
		return []repository.AuthUser{}, nil
	}

	// 构建IN查询的占位符
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, userID := range userIDs {
		placeholders[i] = "$" + fmt.Sprintf("%d", i+1)
		args[i] = userID
	}

	query := fmt.Sprintf(`
		SELECT 
			id, username, email, phone, full_name, department, status,
			created_at, updated_at
		FROM users
		WHERE id IN (%s) AND deleted_at IS NULL
	`, strings.Join(placeholders, ","))

	rows, err := r.multiDB.GetAuthDB().GetPool().Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("批量查询用户信息失败: %w", err)
	}
	defer rows.Close()

	var users []repository.AuthUser
	for rows.Next() {
		var user repository.AuthUser
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email, &user.Phone,
			&user.FullName, &user.Department, &user.Status,
			&user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("解析用户数据失败: %w", err)
		}
		users = append(users, user)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("处理用户列表结果失败: %w", err)
	}

	return users, nil
}
