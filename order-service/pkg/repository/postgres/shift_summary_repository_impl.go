package postgres

import (
	"context"
	"fmt"
	"sync"

	"github.com/jackc/pgconn"
	"github.com/jackc/pgx/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// ShiftSummaryRepositoryImpl PostgreSQL实现
type ShiftSummaryRepositoryImpl struct {
	db *database.Database
}

// NewShiftSummaryRepository 创建新的班结汇总仓储实例
func NewShiftSummaryRepository(db *database.Database) repository.ShiftSummaryRepository {
	return &ShiftSummaryRepositoryImpl{db: db}
}

// GeneratePaymentDetails 生成支付方式明细数据
func (r *ShiftSummaryRepositoryImpl) GeneratePaymentDetails(ctx context.Context, shiftID repository.ID, options *repository.GenerateOptions) error {
	if options == nil {
		options = repository.DefaultGenerateOptions()
	}

	if options.UseTransaction {
		return r.db.RunInTransaction(ctx, func(tx pgx.Tx) error {
			return r.generatePaymentDetailsInTx(ctx, tx, shiftID, options)
		})
	}

	return r.generatePaymentDetailsInTx(ctx, nil, shiftID, options)
}

// generatePaymentDetailsInTx 在事务或直接连接中生成支付明细
func (r *ShiftSummaryRepositoryImpl) generatePaymentDetailsInTx(ctx context.Context, tx pgx.Tx, shiftID repository.ID, options *repository.GenerateOptions) error {
	// 如果强制重新生成，先清理现有数据
	if options.Force {
		if err := r.clearPaymentDetailsInTx(ctx, tx, shiftID); err != nil {
			return fmt.Errorf("清理现有支付明细失败: %w", err)
		}
	}

	// 生成支付方式明细数据（包含员工ID维度）
	query := `
		INSERT INTO shift_payment_details (
			shift_id, employee_id, payment_method, payment_method_name, 
			total_amount, transaction_count
		)
		SELECT 
			$1 as shift_id,
			-- 通过employee_no关联employees表获取真实的employee.id
			e.id as employee_id,
			op.payment_method,
			-- 支付方式显示名称映射
			CASE op.payment_method
				WHEN 'cash' THEN '现金'
				WHEN 'card' THEN '刷卡'
				WHEN 'ewallet' THEN '电子钱包'
				WHEN 'voucher' THEN '代金券'
				WHEN 'fleet' THEN '车队'
				ELSE op.payment_method
			END as payment_method_name,
			SUM(op.amount) as total_amount,
			COUNT(*) as transaction_count
		FROM order_payments op
		JOIN orders o ON o.id = op.order_id
		JOIN shifts s ON s.station_id = o.station_id AND s.id = $1
		LEFT JOIN employees e ON e.employee_no = o.employee_no AND e.deleted_at IS NULL
		WHERE o.created_at BETWEEN s.start_time AND COALESCE(s.end_time, NOW())
			AND op.status = 'completed'
			AND o.status IN ('completed')
		GROUP BY 
			e.id,
			op.payment_method
		ON CONFLICT (shift_id, employee_id, payment_method)
		DO UPDATE SET
			payment_method_name = EXCLUDED.payment_method_name,
			total_amount = EXCLUDED.total_amount,
			transaction_count = EXCLUDED.transaction_count
	`

	var result pgconn.CommandTag
	var err error

	if tx != nil {
		result, err = tx.Exec(ctx, query, shiftID)
	} else {
		result, err = r.db.GetPool().Exec(ctx, query, shiftID)
	}

	if err != nil {
		return fmt.Errorf("生成支付方式明细失败: %w", err)
	}

	// 检查是否有数据被插入
	rowsAffected := result.RowsAffected()
	if options.OnProgress != nil {
		options.OnProgress("payment", 1.0) // 100%完成
	}

	fmt.Printf("班次 %d 支付方式明细生成完成，影响行数: %d\n", shiftID, rowsAffected)
	return nil
}

// GenerateFuelDetails 生成油品明细数据
func (r *ShiftSummaryRepositoryImpl) GenerateFuelDetails(ctx context.Context, shiftID repository.ID, options *repository.GenerateOptions) error {
	if options == nil {
		options = repository.DefaultGenerateOptions()
	}

	if options.UseTransaction {
		return r.db.RunInTransaction(ctx, func(tx pgx.Tx) error {
			return r.generateFuelDetailsInTx(ctx, tx, shiftID, options)
		})
	}

	return r.generateFuelDetailsInTx(ctx, nil, shiftID, options)
}

// generateFuelDetailsInTx 在事务或直接连接中生成油品明细
func (r *ShiftSummaryRepositoryImpl) generateFuelDetailsInTx(ctx context.Context, tx pgx.Tx, shiftID repository.ID, options *repository.GenerateOptions) error {
	// 如果强制重新生成，先清理现有数据
	if options.Force {
		if err := r.clearFuelDetailsInTx(ctx, tx, shiftID); err != nil {
			return fmt.Errorf("清理现有油品明细失败: %w", err)
		}
	}

	// 生成油品明细数据（包含员工ID维度）
	query := `
		INSERT INTO shift_fuel_details (
			shift_id, employee_id, fuel_grade, fuel_type, fuel_name,
			total_volume, gross_amount, total_discount_amount, net_amount,
			average_price, transaction_count
		)
		SELECT 
			$1 as shift_id,
			-- 优先从关联的orders表通过employees表获取employee_id
			-- 暂时简化逻辑，只使用employees表的ID，避免类型不匹配
			e.id as employee_id,
			ft.fuel_grade,
			ft.fuel_type,
			-- 油品显示名称映射
			CASE ft.fuel_grade
				WHEN '92' THEN '92号汽油'
				WHEN '95' THEN '95号汽油'
				WHEN '98' THEN '98号汽油'
				WHEN 'diesel' THEN '柴油'
				ELSE CONCAT(ft.fuel_grade, '号', ft.fuel_type)
			END as fuel_name,
			SUM(ft.volume) as total_volume,
			-- 毛销售额（销量 × 单价）
			SUM(ft.amount) as gross_amount,
			-- 总优惠金额（从订单项获取折扣）
			SUM(COALESCE(oi.discount_amount, 0)) as total_discount_amount,
			-- 净销售额（毛销售额 - 折扣）
			SUM(ft.amount) - SUM(COALESCE(oi.discount_amount, 0)) as net_amount,
			-- 平均单价（基于毛销售额）
			CASE
				WHEN SUM(ft.volume) > 0 THEN SUM(ft.amount) / SUM(ft.volume)
				ELSE 0
			END as average_price,
			COUNT(*) as transaction_count
		FROM fuel_transactions ft
		JOIN shifts s ON s.station_id = ft.station_id AND s.id = $1
		LEFT JOIN fuel_transaction_order_links ftol ON ft.id = ftol.fuel_transaction_id
		LEFT JOIN orders o ON ftol.order_id = o.id
		LEFT JOIN employees e ON e.employee_no = o.employee_no AND e.deleted_at IS NULL
		LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.product_type = 'fuel'
		WHERE ft.created_at BETWEEN s.start_time AND COALESCE(s.end_time, NOW())
			AND ft.status = 'processed'
		GROUP BY
			e.id,
			ft.fuel_grade,
			ft.fuel_type
		ON CONFLICT (shift_id, employee_id, fuel_grade)
		DO UPDATE SET
			fuel_type = EXCLUDED.fuel_type,
			fuel_name = EXCLUDED.fuel_name,
			total_volume = EXCLUDED.total_volume,
			gross_amount = EXCLUDED.gross_amount,
			total_discount_amount = EXCLUDED.total_discount_amount,
			net_amount = EXCLUDED.net_amount,
			average_price = EXCLUDED.average_price,
			transaction_count = EXCLUDED.transaction_count
	`

	var result pgconn.CommandTag
	var err error

	if tx != nil {
		result, err = tx.Exec(ctx, query, shiftID)
	} else {
		result, err = r.db.GetPool().Exec(ctx, query, shiftID)
	}

	if err != nil {
		return fmt.Errorf("生成油品明细失败: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if options.OnProgress != nil {
		options.OnProgress("fuel", 1.0) // 100%完成
	}

	fmt.Printf("班次 %d 油品明细生成完成，影响行数: %d\n", shiftID, rowsAffected)
	return nil
}

// GenerateMerchandiseDetails 生成非油品明细数据
func (r *ShiftSummaryRepositoryImpl) GenerateMerchandiseDetails(ctx context.Context, shiftID repository.ID, options *repository.GenerateOptions) error {
	if options == nil {
		options = repository.DefaultGenerateOptions()
	}

	if options.UseTransaction {
		return r.db.RunInTransaction(ctx, func(tx pgx.Tx) error {
			return r.generateMerchandiseDetailsInTx(ctx, tx, shiftID, options)
		})
	}

	return r.generateMerchandiseDetailsInTx(ctx, nil, shiftID, options)
}

// generateMerchandiseDetailsInTx 在事务或直接连接中生成非油品明细
func (r *ShiftSummaryRepositoryImpl) generateMerchandiseDetailsInTx(ctx context.Context, tx pgx.Tx, shiftID repository.ID, options *repository.GenerateOptions) error {
	// 如果强制重新生成，先清理现有数据
	if options.Force {
		if err := r.clearMerchandiseDetailsInTx(ctx, tx, shiftID); err != nil {
			return fmt.Errorf("清理现有非油品明细失败: %w", err)
		}
	}

	// 生成非油品明细数据（包含员工ID维度）
	query := `
		INSERT INTO shift_merchandise_details (
			shift_id, employee_id, product_id, product_name, product_type, product_category,
			total_quantity, unit_price, total_amount, total_discount_amount, 
			net_amount, transaction_count
		)
		SELECT 
			$1 as shift_id,
			-- 通过employee_no关联employees表获取真实的employee.id
			e.id as employee_id,
			oi.product_id,
			oi.product_name,
			oi.product_type,
			NULL as product_category, -- 可以从产品表获取
			SUM(oi.quantity) as total_quantity,
			AVG(oi.unit_price) as unit_price,
			SUM(oi.total_price) as total_amount,
			SUM(oi.discount_amount) as total_discount_amount,
			SUM(oi.final_price) as net_amount,
			COUNT(DISTINCT o.id) as transaction_count
		FROM order_items oi
		JOIN orders o ON oi.order_id = o.id
		JOIN shifts s ON s.station_id = o.station_id AND s.id = $1
		LEFT JOIN employees e ON e.employee_no = o.employee_no AND e.deleted_at IS NULL
		WHERE o.created_at BETWEEN s.start_time AND COALESCE(s.end_time, NOW())
			AND o.status = 'completed'
			AND oi.product_type != 'fuel' -- 只统计非油品
		GROUP BY 
			e.id,
			oi.product_id, 
			oi.product_name, 
			oi.product_type
		ON CONFLICT (shift_id, employee_id, product_id)
		DO UPDATE SET
			product_name = EXCLUDED.product_name,
			product_type = EXCLUDED.product_type,
			product_category = EXCLUDED.product_category,
			total_quantity = EXCLUDED.total_quantity,
			unit_price = EXCLUDED.unit_price,
			total_amount = EXCLUDED.total_amount,
			total_discount_amount = EXCLUDED.total_discount_amount,
			net_amount = EXCLUDED.net_amount,
			transaction_count = EXCLUDED.transaction_count
	`

	var result pgconn.CommandTag
	var err error

	if tx != nil {
		result, err = tx.Exec(ctx, query, shiftID)
	} else {
		result, err = r.db.GetPool().Exec(ctx, query, shiftID)
	}

	if err != nil {
		return fmt.Errorf("生成非油品明细失败: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if options.OnProgress != nil {
		options.OnProgress("merchandise", 1.0) // 100%完成
	}

	fmt.Printf("班次 %d 非油品明细生成完成，影响行数: %d\n", shiftID, rowsAffected)
	return nil
}

// GenerateShiftSummary 生成班次汇总数据（主方法）
func (r *ShiftSummaryRepositoryImpl) GenerateShiftSummary(ctx context.Context, shiftID repository.ID, options *repository.GenerateOptions) (*repository.ShiftSummaryDTO, error) {
	if options == nil {
		options = repository.DefaultGenerateOptions()
	}

	// 并行生成各类明细数据
	if options.Parallel {
		var wg sync.WaitGroup
		errChan := make(chan error, 3)

		// 并行生成支付明细
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := r.GeneratePaymentDetails(ctx, shiftID, options); err != nil {
				errChan <- fmt.Errorf("生成支付明细失败: %w", err)
			}
		}()

		// 并行生成油品明细
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := r.GenerateFuelDetails(ctx, shiftID, options); err != nil {
				errChan <- fmt.Errorf("生成油品明细失败: %w", err)
			}
		}()

		// 并行生成非油品明细
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := r.GenerateMerchandiseDetails(ctx, shiftID, options); err != nil {
				errChan <- fmt.Errorf("生成非油品明细失败: %w", err)
			}
		}()

		wg.Wait()
		close(errChan)

		// 检查错误
		for err := range errChan {
			if err != nil {
				return nil, err
			}
		}
	} else {
		// 顺序生成
		if err := r.GeneratePaymentDetails(ctx, shiftID, options); err != nil {
			return nil, fmt.Errorf("生成支付明细失败: %w", err)
		}
		if err := r.GenerateFuelDetails(ctx, shiftID, options); err != nil {
			return nil, fmt.Errorf("生成油品明细失败: %w", err)
		}
		if err := r.GenerateMerchandiseDetails(ctx, shiftID, options); err != nil {
			return nil, fmt.Errorf("生成非油品明细失败: %w", err)
		}
	}

	// 生成完成后获取汇总数据
	return r.GetShiftSummary(ctx, shiftID)
}

// GetShiftSummary 获取班次汇总数据
func (r *ShiftSummaryRepositoryImpl) GetShiftSummary(ctx context.Context, shiftID repository.ID) (*repository.ShiftSummaryDTO, error) {
	// 获取班次基础信息
	shiftQuery := `
		SELECT shift_number, station_id, start_time, end_time
		FROM shifts 
		WHERE id = $1
	`

	var summary repository.ShiftSummaryDTO
	err := r.db.GetPool().QueryRow(ctx, shiftQuery, shiftID).Scan(
		&summary.ShiftNumber, &summary.StationID,
		&summary.StartTime, &summary.EndTime,
	)
	if err != nil {
		return nil, fmt.Errorf("获取班次信息失败: %w", err)
	}

	// 获取支付方式明细
	paymentQuery := `
		SELECT payment_method, total_amount, transaction_count
		FROM shift_payment_details 
		WHERE shift_id = $1
		ORDER BY total_amount DESC
	`

	paymentRows, err := r.db.GetPool().Query(ctx, paymentQuery, shiftID)
	if err != nil {
		return nil, fmt.Errorf("获取支付明细失败: %w", err)
	}
	defer paymentRows.Close()

	summary.PaymentSummary = make(map[string]float64)
	for paymentRows.Next() {
		var method string
		var amount float64
		var count int
		if err := paymentRows.Scan(&method, &amount, &count); err != nil {
			return nil, fmt.Errorf("扫描支付明细失败: %w", err)
		}
		summary.PaymentSummary[method] = amount
		summary.TotalSales += amount
	}

	// 获取油品明细
	fuelQuery := `
		SELECT fuel_grade, total_volume, net_amount, transaction_count
		FROM shift_fuel_details 
		WHERE shift_id = $1
		ORDER BY total_volume DESC
	`

	fuelRows, err := r.db.GetPool().Query(ctx, fuelQuery, shiftID)
	if err != nil {
		return nil, fmt.Errorf("获取油品明细失败: %w", err)
	}
	defer fuelRows.Close()

	summary.FuelSummary = make(map[string]float64)
	for fuelRows.Next() {
		var grade string
		var volume, amount float64
		var count int
		if err := fuelRows.Scan(&grade, &volume, &amount, &count); err != nil {
			return nil, fmt.Errorf("扫描油品明细失败: %w", err)
		}
		summary.FuelSummary[grade] = volume
		summary.TotalFuelVolume += volume
		summary.TotalFuelSales += amount
	}

	// 获取非油品汇总
	merchandiseQuery := `
		SELECT 
			COALESCE(SUM(total_quantity), 0) as quantity,
			COALESCE(SUM(total_amount), 0) as sales,
			COALESCE(SUM(total_discount_amount), 0) as discount,
			COALESCE(SUM(net_amount), 0) as net_sales
		FROM shift_merchandise_details 
		WHERE shift_id = $1
	`

	err = r.db.GetPool().QueryRow(ctx, merchandiseQuery, shiftID).Scan(
		&summary.MerchandiseSummary.TotalQuantity,
		&summary.MerchandiseSummary.TotalSales,
		&summary.MerchandiseSummary.TotalDiscount,
		&summary.MerchandiseSummary.NetSales,
	)
	if err != nil && err != pgx.ErrNoRows {
		return nil, fmt.Errorf("获取非油品汇总失败: %w", err)
	}

	return &summary, nil
}

// clearPaymentDetailsInTx 清理支付明细（事务内）
func (r *ShiftSummaryRepositoryImpl) clearPaymentDetailsInTx(ctx context.Context, tx pgx.Tx, shiftID repository.ID) error {
	query := `DELETE FROM shift_payment_details WHERE shift_id = $1`

	if tx != nil {
		_, err := tx.Exec(ctx, query, shiftID)
		return err
	} else {
		_, err := r.db.GetPool().Exec(ctx, query, shiftID)
		return err
	}
}

// clearFuelDetailsInTx 清理油品明细（事务内）
func (r *ShiftSummaryRepositoryImpl) clearFuelDetailsInTx(ctx context.Context, tx pgx.Tx, shiftID repository.ID) error {
	query := `DELETE FROM shift_fuel_details WHERE shift_id = $1`

	if tx != nil {
		_, err := tx.Exec(ctx, query, shiftID)
		return err
	} else {
		_, err := r.db.GetPool().Exec(ctx, query, shiftID)
		return err
	}
}

// clearMerchandiseDetailsInTx 清理非油品明细（事务内）
func (r *ShiftSummaryRepositoryImpl) clearMerchandiseDetailsInTx(ctx context.Context, tx pgx.Tx, shiftID repository.ID) error {
	query := `DELETE FROM shift_merchandise_details WHERE shift_id = $1`

	if tx != nil {
		_, err := tx.Exec(ctx, query, shiftID)
		return err
	} else {
		_, err := r.db.GetPool().Exec(ctx, query, shiftID)
		return err
	}
}

// GetPaymentDetails 获取支付方式明细
func (r *ShiftSummaryRepositoryImpl) GetPaymentDetails(ctx context.Context, shiftID repository.ID) ([]repository.ShiftPaymentDetail, error) {
	query := `
		SELECT 
			id, shift_id, employee_id, payment_method, payment_method_name,
			total_amount, transaction_count, created_at
		FROM shift_payment_details 
		WHERE shift_id = $1
		ORDER BY total_amount DESC
	`

	rows, err := r.db.GetPool().Query(ctx, query, shiftID)
	if err != nil {
		return nil, fmt.Errorf("查询支付方式明细失败: %w", err)
	}
	defer rows.Close()

	var details []repository.ShiftPaymentDetail
	for rows.Next() {
		var detail repository.ShiftPaymentDetail
		err := rows.Scan(
			&detail.ID, &detail.ShiftID, &detail.EmployeeID,
			&detail.PaymentMethod, &detail.PaymentMethodName,
			&detail.TotalAmount, &detail.TransactionCount, &detail.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描支付方式明细失败: %w", err)
		}
		details = append(details, detail)
	}

	return details, nil
}

func (r *ShiftSummaryRepositoryImpl) ClearPaymentDetails(ctx context.Context, shiftID repository.ID) error {
	return r.clearPaymentDetailsInTx(ctx, nil, shiftID)
}

// GetFuelDetails 获取油品明细
func (r *ShiftSummaryRepositoryImpl) GetFuelDetails(ctx context.Context, shiftID repository.ID) ([]repository.ShiftFuelDetail, error) {
	query := `
		SELECT 
			id, shift_id, employee_id, fuel_grade, fuel_type, fuel_name,
			total_volume, gross_amount, total_discount_amount, net_amount,
			average_price, transaction_count, created_at
		FROM shift_fuel_details 
		WHERE shift_id = $1
		ORDER BY total_volume DESC
	`

	rows, err := r.db.GetPool().Query(ctx, query, shiftID)
	if err != nil {
		return nil, fmt.Errorf("查询油品明细失败: %w", err)
	}
	defer rows.Close()

	var details []repository.ShiftFuelDetail
	for rows.Next() {
		var detail repository.ShiftFuelDetail
		err := rows.Scan(
			&detail.ID, &detail.ShiftID, &detail.EmployeeID,
			&detail.FuelGrade, &detail.FuelType, &detail.FuelName,
			&detail.TotalVolume, &detail.GrossAmount, &detail.TotalDiscountAmount, &detail.NetAmount,
			&detail.AveragePrice, &detail.TransactionCount, &detail.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描油品明细失败: %w", err)
		}
		details = append(details, detail)
	}

	return details, nil
}

func (r *ShiftSummaryRepositoryImpl) ClearFuelDetails(ctx context.Context, shiftID repository.ID) error {
	return r.clearFuelDetailsInTx(ctx, nil, shiftID)
}

// GetMerchandiseDetails 获取非油品明细
func (r *ShiftSummaryRepositoryImpl) GetMerchandiseDetails(ctx context.Context, shiftID repository.ID) ([]repository.ShiftMerchandiseDetail, error) {
	query := `
		SELECT 
			id, shift_id, employee_id, product_id, product_name, product_type, product_category,
			total_quantity, unit_price, total_amount, total_discount_amount, net_amount,
			transaction_count, created_at
		FROM shift_merchandise_details 
		WHERE shift_id = $1
		ORDER BY net_amount DESC
	`

	rows, err := r.db.GetPool().Query(ctx, query, shiftID)
	if err != nil {
		return nil, fmt.Errorf("查询非油品明细失败: %w", err)
	}
	defer rows.Close()

	var details []repository.ShiftMerchandiseDetail
	for rows.Next() {
		var detail repository.ShiftMerchandiseDetail
		err := rows.Scan(
			&detail.ID, &detail.ShiftID, &detail.EmployeeID,
			&detail.ProductID, &detail.ProductName, &detail.ProductType, &detail.ProductCategory,
			&detail.TotalQuantity, &detail.UnitPrice, &detail.TotalAmount, &detail.TotalDiscountAmount, &detail.NetAmount,
			&detail.TransactionCount, &detail.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描非油品明细失败: %w", err)
		}
		details = append(details, detail)
	}

	return details, nil
}

func (r *ShiftSummaryRepositoryImpl) ClearMerchandiseDetails(ctx context.Context, shiftID repository.ID) error {
	return r.clearMerchandiseDetailsInTx(ctx, nil, shiftID)
}

func (r *ShiftSummaryRepositoryImpl) ClearShiftSummary(ctx context.Context, shiftID repository.ID) error {
	return r.db.RunInTransaction(ctx, func(tx pgx.Tx) error {
		if err := r.clearPaymentDetailsInTx(ctx, tx, shiftID); err != nil {
			return err
		}
		if err := r.clearFuelDetailsInTx(ctx, tx, shiftID); err != nil {
			return err
		}
		return r.clearMerchandiseDetailsInTx(ctx, tx, shiftID)
	})
}

func (r *ShiftSummaryRepositoryImpl) BatchGenerateShiftSummaries(ctx context.Context, shiftIDs []repository.ID, options *repository.GenerateOptions) error {
	return fmt.Errorf("BatchGenerateShiftSummaries not implemented")
}

func (r *ShiftSummaryRepositoryImpl) ValidateShiftSummary(ctx context.Context, shiftID repository.ID) error {
	return fmt.Errorf("ValidateShiftSummary not implemented")
}

func (r *ShiftSummaryRepositoryImpl) RepairShiftSummary(ctx context.Context, shiftID repository.ID) error {
	return fmt.Errorf("RepairShiftSummary not implemented")
}
