package repository

import (
	"context"
	"time"
)

// StaffCardRepository 员工卡存储库接口
type StaffCardRepository interface {
	// Create 创建员工卡
	Create(ctx context.Context, staffCard StaffCard) (StaffCard, error)

	// Update 更新员工卡
	Update(ctx context.Context, staffCard StaffCard) (StaffCard, error)

	// Get 根据ID获取员工卡
	Get(ctx context.Context, id ID) (StaffCard, error)

	// GetByCardNumber 根据卡号获取员工卡
	GetByCardNumber(ctx context.Context, cardNumber string) (StaffCard, error)

	// GetByUserID 根据用户ID获取员工卡列表
	GetByUserID(ctx context.Context, userID string) ([]StaffCard, error)

	// List 获取员工卡列表
	List(ctx context.Context, filter StaffCardFilter, pagination Pagination, sort SortOrder) ([]StaffCard, int, error)

	// Delete 删除员工卡（软删除）
	Delete(ctx context.Context, id ID) error

	// ValidateCard 验证员工卡是否有效（状态、有效期等）
	ValidateCard(ctx context.Context, cardNumber string, stationID *int64) (StaffCardValidationResult, error)

	// GetUserInfo 获取员工卡关联的用户信息（跨库查询）
	GetUserInfo(ctx context.Context, userID string) (AuthUser, error)

	// GetWithUserInfo 获取员工卡信息并包含用户信息
	GetWithUserInfo(ctx context.Context, id ID) (StaffCard, error)

	// ListWithUserInfo 获取员工卡列表并包含用户信息
	ListWithUserInfo(ctx context.Context, filter StaffCardFilter, pagination Pagination, sort SortOrder) ([]StaffCard, int, error)

	// UpdateStatus 更新员工卡状态
	UpdateStatus(ctx context.Context, id ID, status StaffCardStatus, reason string) error

	// ExtendValidity 延长员工卡有效期
	ExtendValidity(ctx context.Context, id ID, newValidUntil *time.Time) error

	// GetActiveCardsByStation 获取指定站点的活跃员工卡
	GetActiveCardsByStation(ctx context.Context, stationID int64) ([]StaffCard, error)
}
