-- 检查shift表时区修复情况的脚本

-- 设置schema和时区
SET search_path TO order_schema, pg_catalog;
SET timezone TO 'Asia/Jakarta';

-- 1. 检查数据库时区设置
SELECT 
    'Database Timezone Check' as check_type,
    current_setting('timezone') as current_timezone,
    NOW() as current_time,
    EXTRACT(timezone_hour FROM NOW()) as current_tz_hour;

-- 2. 检查shift表的时区分布
SELECT 
    'Timezone Distribution' as check_type,
    EXTRACT(timezone_hour FROM start_time) as timezone_hour,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM shifts 
WHERE deleted_at IS NULL
GROUP BY EXTRACT(timezone_hour FROM start_time)
ORDER BY timezone_hour;

-- 3. 检查最近的班次记录
SELECT 
    'Recent Shifts Check' as check_type,
    id,
    shift_number,
    station_id,
    start_time,
    end_time,
    EXTRACT(timezone_hour FROM start_time) as start_tz,
    CASE 
        WHEN end_time IS NOT NULL THEN EXTRACT(timezone_hour FROM end_time)
        ELSE NULL 
    END as end_tz,
    created_at
FROM shifts 
WHERE deleted_at IS NULL
ORDER BY created_at DESC 
LIMIT 5;

-- 4. 检查是否还有时区问题的记录
SELECT 
    'Problem Records Check' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN EXTRACT(timezone_hour FROM start_time) != 7 THEN 1 END) as wrong_start_tz,
    COUNT(CASE WHEN end_time IS NOT NULL AND EXTRACT(timezone_hour FROM end_time) != 7 THEN 1 END) as wrong_end_tz
FROM shifts 
WHERE deleted_at IS NULL;

-- 5. 检查活跃班次的时区
SELECT 
    'Active Shifts Check' as check_type,
    id,
    shift_number,
    station_id,
    start_time,
    EXTRACT(timezone_hour FROM start_time) as start_tz,
    'Active' as status
FROM shifts 
WHERE deleted_at IS NULL 
    AND end_time IS NULL
ORDER BY start_time DESC;

-- 6. 检查今天创建的班次
SELECT 
    'Today Shifts Check' as check_type,
    id,
    shift_number,
    station_id,
    start_time,
    end_time,
    EXTRACT(timezone_hour FROM start_time) as start_tz,
    CASE 
        WHEN end_time IS NOT NULL THEN EXTRACT(timezone_hour FROM end_time)
        ELSE NULL 
    END as end_tz
FROM shifts 
WHERE deleted_at IS NULL 
    AND DATE(created_at) = CURRENT_DATE
ORDER BY created_at DESC;

-- 7. 统计报告
SELECT 
    'Summary Report' as check_type,
    COUNT(*) as total_shifts,
    COUNT(CASE WHEN end_time IS NULL THEN 1 END) as active_shifts,
    COUNT(CASE WHEN end_time IS NOT NULL THEN 1 END) as completed_shifts,
    COUNT(CASE WHEN EXTRACT(timezone_hour FROM start_time) = 7 THEN 1 END) as correct_start_tz,
    COUNT(CASE WHEN end_time IS NOT NULL AND EXTRACT(timezone_hour FROM end_time) = 7 THEN 1 END) as correct_end_tz,
    ROUND(
        COUNT(CASE WHEN EXTRACT(timezone_hour FROM start_time) = 7 THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as correct_start_tz_percentage
FROM shifts 
WHERE deleted_at IS NULL;
